<template>
  <div class="profile">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <h2>个人信息</h2>
        </div>
      </template>
      <el-form :model="userForm" label-width="100px" v-loading="loading">
        <el-form-item label="用户名">
          <el-input v-model="userForm.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="userForm.email"></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="userForm.fullName"></el-input>
        </el-form-item>
        <el-form-item label="电话">
          <el-input v-model="userForm.phoneNumber"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-tag v-for="role in userForm.roles" :key="role" style="margin-right: 5px;">
            {{ formatRole(role) }}
          </el-tag>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateProfile">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'Profile',
  data() {
    return {
      loading: false,
      userForm: {
        username: '',
        email: '',
        fullName: '',
        phoneNumber: '',
        roles: []
      }
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    }
  },
  created() {
    this.loadUserData();
  },
  methods: {
    loadUserData() {
      this.loading = true;
      if (this.currentUser) {
        UserService.getUserById(this.currentUser.id)
          .then(response => {
            this.userForm = {
              username: response.data.username,
              email: response.data.email,
              fullName: response.data.fullName,
              phoneNumber: response.data.phoneNumber,
              roles: this.currentUser.roles
            };
            this.loading = false;
          })
          .catch(error => {
            console.error('Error loading user data:', error);
            this.loading = false;
          });
      }
    },
    updateProfile() {
      this.loading = true;
      UserService.updateUser(this.currentUser.id, this.userForm)
        .then(() => {
          this.$message.success('个人信息更新成功');
          this.loading = false;
        })
        .catch(error => {
          console.error('Error updating profile:', error);
          this.$message.error('更新失败: ' + (error.response?.data?.message || error.message));
          this.loading = false;
        });
    },
    formatRole(role) {
      switch(role) {
        case 'ROLE_USER': return '普通用户';
        case 'ROLE_ADMIN': return '管理员';
        case 'ROLE_SUPER_ADMIN': return '超级管理员';
        default: return role;
      }
    }
  }
};
</script>

<style scoped>
.profile {
  padding: 20px;
}

.profile-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
