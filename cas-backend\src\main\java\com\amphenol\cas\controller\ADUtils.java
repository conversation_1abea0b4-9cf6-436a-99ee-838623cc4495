package com.amphenol.cas.controller;

import java.util.Properties;
import javax.naming.*;
import javax.naming.directory.*;
import javax.naming.ldap.*;

public class ADUtils {
    public static void main(String[] args) {
        Properties env = new Properties();
        String adminName = "<EMAIL>";
        String adminPassword = "Q789+a456+z123.#";
        String ldapURL = "LDAP://***************:389";

        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, adminName);
        env.put(Context.SECURITY_CREDENTIALS, adminPassword);
        env.put(Context.PROVIDER_URL, ldapURL);

        // 关键：启用引用跟踪
        env.put(Context.REFERRAL, "follow");

        LdapContext ctx = null;
        try {
            ctx = new InitialLdapContext(env, null);
            System.out.println("成功连接到AD域");

            // 配置分页控制
            int pageSize = 500; // 每页大小（AD最大支持1000）
            byte[] cookie = null;
            int totalCount = 0;

            // 设置搜索参数
            String searchBase = "DC=JET,DC=com";
            String searchFilter = "(&(objectClass=user)(objectCategory=person))";
            String[] returnedAtts = {"userPrincipalName", "sAMAccountName", "mail", "displayName"};

            SearchControls searchCtls = new SearchControls();
            searchCtls.setSearchScope(SearchControls.SUBTREE_SCOPE);
            searchCtls.setReturningAttributes(returnedAtts);

            do {
                // 设置分页控制参数
                ctx.setRequestControls(new Control[]{
                        new PagedResultsControl(pageSize, cookie, Control.CRITICAL)
                });

                // 执行查询
                NamingEnumeration<SearchResult> results = ctx.search(searchBase, searchFilter, searchCtls);

                // 处理当前页结果
                while (results != null && results.hasMoreElements()) {
                    SearchResult sr = results.next();
                    totalCount++;

                    Attributes attrs = sr.getAttributes();
                    if (attrs != null) {
                        System.out.println("\n记录 #" + totalCount + ": " + sr.getName());
                        NamingEnumeration<? extends Attribute> allAttrs = attrs.getAll();
                        while (allAttrs.hasMore()) {
                            Attribute attr = allAttrs.next();
                            System.out.print(attr.getID() + ": ");
                            NamingEnumeration<?> values = attr.getAll();
                            while (values.hasMore()) {
                                System.out.print(values.next());
                                if (values.hasMore()) System.out.print(", ");
                            }
                            System.out.println();
                        }
                    }
                }

                // 获取下一页cookie
                cookie = null;
                Control[] responseControls = ctx.getResponseControls();
                if (responseControls != null) {
                    for (Control control : responseControls) {
                        if (control instanceof PagedResultsResponseControl) {
                            PagedResultsResponseControl prrc = (PagedResultsResponseControl) control;
                            cookie = prrc.getCookie();
                        }
                    }
                }

                System.out.println("已获取 " + totalCount + " 条记录，继续下一页...");

            } while (cookie != null);

            System.out.println("查询完成！总记录数: " + totalCount);

        } catch (PartialResultException e) {
            System.err.println("部分结果异常: " + e.getMessage());
            System.out.println("提示: 这通常发生在跨域引用时，已获取的数据仍然有效");
        } catch (NamingException e) {
            e.printStackTrace();
            System.err.println("LDAP查询错误: " + e);
        } catch (java.io.IOException e) {
            e.printStackTrace();
            System.err.println("分页控制IO错误: " + e);
        } finally {
            if (ctx != null) {
                try { ctx.close(); } catch (NamingException e) {}
            }
        }
    }
}