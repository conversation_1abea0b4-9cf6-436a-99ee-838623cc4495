# 安费诺统一认证系统（CAS）架构文档

## 1. 系统概述

安费诺统一认证系统（CAS）是一个中央认证服务，提供单点登录（SSO）功能，允许用户使用一组凭据访问多个应用程序。系统基于OAuth 2.0和JWT实现，采用前后端分离架构。

## 2. 系统架构

### 2.1 总体架构

CAS系统采用三层架构：
- 表示层：Vue.js前端应用
- 业务逻辑层：Spring Boot后端应用
- 数据访问层：关系型数据库（H2/MySQL）

![系统架构图](./images/architecture.png)

### 2.2 技术栈

#### 前端技术栈
- Vue.js 3.x：前端框架
- Vuex：状态管理
- Vue Router：路由管理
- Element Plus：UI组件库
- Axios：HTTP客户端

#### 后端技术栈
- Java 8
- Spring Boot 2.7.x：应用框架
- Spring Security：安全框架
- Spring Data JPA：数据访问
- JWT：令牌生成和验证
- H2/MySQL：数据库

### 2.3 模块划分

#### 前端模块
- 认证模块：登录、注册、找回密码
- 用户管理模块：用户信息管理、角色管理
- 应用管理模块：应用注册、权限配置
- 日志监控模块：系统日志、审计日志

#### 后端模块
- 认证模块：用户认证、令牌管理
- 用户管理模块：用户CRUD、角色管理
- 应用管理模块：应用CRUD、权限管理
- 日志模块：系统日志、审计日志
- 安全模块：权限控制、数据加密

## 3. 数据模型

### 3.1 实体关系图

![实体关系图](./images/erd.png)

### 3.2 主要实体

#### 用户（User）
- id：用户ID
- username：用户名
- email：邮箱
- password：密码（加密存储）
- fullName：姓名
- phoneNumber：电话号码
- enabled：是否启用
- accountNonExpired：账户是否未过期
- accountNonLocked：账户是否未锁定
- credentialsNonExpired：凭证是否未过期
- roles：用户角色（多对多关系）

#### 角色（Role）
- id：角色ID
- name：角色名称（枚举：ROLE_USER, ROLE_ADMIN, ROLE_SUPER_ADMIN）

#### 应用（Application）
- id：应用ID
- name：应用名称
- description：应用描述
- redirectUrl：回调URL
- clientId：客户端ID
- clientSecret：客户端密钥
- enabled：是否启用
- requiredRoles：所需角色（多对多关系）

#### 审计日志（AuditLog）
- id：日志ID
- action：操作类型
- username：用户名
- details：详细信息
- ipAddress：IP地址
- userAgent：用户代理
- timestamp：时间戳

## 4. 认证流程

### 4.1 用户认证流程

1. 用户访问前端应用
2. 用户输入用户名和密码
3. 前端将凭据发送到后端认证接口
4. 后端验证凭据并生成JWT令牌
5. 前端存储令牌并使用它访问受保护的资源

### 4.2 单点登录流程

1. 用户访问接入应用
2. 应用将用户重定向到CAS登录页面
3. 用户在CAS系统中登录
4. CAS系统将用户重定向回应用，并提供授权码
5. 应用使用授权码向CAS系统请求访问令牌
6. CAS系统返回访问令牌和用户信息
7. 应用使用访问令牌获取用户信息并创建会话

### 4.3 令牌管理

- 访问令牌：JWT格式，有效期1小时
- 刷新令牌：有效期30天，用于获取新的访问令牌
- 令牌存储：客户端本地存储（localStorage）

## 5. 安全措施

### 5.1 密码安全
- 密码加密：使用BCrypt算法
- 密码策略：最小长度6个字符，包含字母和数字

### 5.2 通信安全
- HTTPS：所有通信使用HTTPS加密
- CORS：配置跨域资源共享策略

### 5.3 授权控制
- 基于角色的访问控制（RBAC）
- 方法级安全注解
- JWT令牌验证

### 5.4 防护措施
- CSRF防护：使用state参数
- XSS防护：输入验证和输出编码
- SQL注入防护：使用参数化查询

## 6. 部署架构

### 6.1 开发环境
- 前端：本地开发服务器（npm run serve）
- 后端：本地Spring Boot应用
- 数据库：H2内存数据库

### 6.2 生产环境
- 前端：Nginx服务器
- 后端：Docker容器化部署
- 数据库：MySQL集群
- 负载均衡：Nginx负载均衡
- 缓存：Redis缓存

### 6.3 部署拓扑图

![部署拓扑图](./images/deployment.png)

## 7. 性能与扩展性

### 7.1 性能优化
- 数据库索引优化
- 连接池配置
- 静态资源缓存
- API响应压缩

### 7.2 扩展性设计
- 水平扩展：多实例部署
- 垂直扩展：增加服务器资源
- 数据库读写分离
- 微服务架构演进计划

## 8. 监控与运维

### 8.1 监控指标
- 系统健康状态
- API响应时间
- 用户认证成功/失败率
- 系统资源使用率

### 8.2 日志管理
- 应用日志：记录系统运行状态
- 审计日志：记录用户操作
- 安全日志：记录安全事件

### 8.3 备份策略
- 数据库定时备份
- 配置文件备份
- 灾难恢复计划

## 9. 未来规划

### 9.1 功能增强
- 多因素认证（MFA）
- 社交媒体登录集成
- 自助服务门户

### 9.2 技术升级
- 微服务架构转型
- 容器化全面部署
- API网关集成

## 10. 附录

### 10.1 术语表
- CAS：Central Authentication Service，中央认证服务
- SSO：Single Sign-On，单点登录
- JWT：JSON Web Token，用于安全传输信息的开放标准
- OAuth：开放授权协议
- RBAC：Role-Based Access Control，基于角色的访问控制

### 10.2 参考文档
- Spring Security文档
- OAuth 2.0规范
- JWT规范
