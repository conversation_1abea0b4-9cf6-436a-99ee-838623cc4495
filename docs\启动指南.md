# 安费诺统一认证系统（CAS）启动指南

本文档提供了如何启动和运行安费诺统一认证系统（CAS）的详细步骤，包括后端Java服务和前端Vue应用。

## 目录

- [系统要求](#系统要求)
- [数据库配置](#数据库配置)
- [后端服务启动](#后端服务启动)
- [前端应用启动](#前端应用启动)
- [访问系统](#访问系统)
- [常见问题](#常见问题)

## 系统要求

### 开发环境

- JDK 8+
- Maven 3.6+
- Node.js 14+
- npm 6+ 或 yarn 1.22+
- MySQL 8.0+

### 生产环境

- JDK 8+
- MySQL 8.0+
- Nginx (用于部署前端)
- 至少2GB内存的服务器

## 数据库配置

### 1. 创建数据库

首先，需要在MySQL中创建数据库：

```sql
CREATE DATABASE amphenol_cas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 配置数据库连接

修改后端应用的配置文件 `cas-backend/src/main/resources/application.yml`，设置正确的数据库连接信息：

```yaml
spring:
  datasource:
    url: *****************************************************************************************************
    username: root  # 修改为您的MySQL用户名
    password: your_password  # 修改为您的MySQL密码
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## 后端服务启动

### 开发环境启动

1. 进入后端项目目录：

```bash
cd cas-backend
```

2. 使用Maven编译项目：

```bash
mvn clean install
```

3. 启动Spring Boot应用：

```bash
mvn org.springframework.boot:spring-boot-maven-plugin:2.7.0:run
```

```bash
或者：mvn -f C:\Users\<USER>\script\autox_script\cas\cas-backend\pom.xml clean compile

mvn -f C:\Users\<USER>\script\autox_script\cas\cas-backend\pom.xml spring-boot:run
```
后端服务将在 http://localhost:8080/api 运行。

### 生产环境部署

1. 构建可执行JAR包：

```bash
cd cas-backend
mvn clean package -DskipTests
```

2. 将生成的JAR包（位于`target`目录）复制到服务器。

3. 在服务器上运行：

```bash
java -jar cas-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

> 注意：生产环境建议配置单独的`application-prod.yml`文件，包含生产环境特定的配置。

## 前端应用启动

### 开发环境启动

1. 进入前端项目目录：

```bash
cd cas-frontend
```

2. 安装依赖：

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

3. 启动开发服务器：

```bash
# 使用npm
npm run serve

# 或使用yarn
yarn serve
```

前端应用将在 http://localhost:8081 运行。

### 生产环境部署

1. 构建生产版本：

```bash
cd cas-frontend

# 使用npm
npm run build

# 或使用yarn
yarn build
```

2. 将生成的`dist`目录中的文件部署到Web服务器（如Nginx）。

3. Nginx配置示例：

```nginx
server {
    listen 80;
    server_name cas.amphenol.com;  # 替换为您的域名

    root /path/to/cas-frontend/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 访问系统

### 开发环境

- 前端：http://localhost:8081
- 后端API：http://localhost:8080/api
- H2数据库控制台（如果启用）：http://localhost:8080/api/h2-console

### 生产环境

- 通过配置的域名访问，例如：http://cas.amphenol.com

### 默认管理员账户

系统初始化时会创建一个默认的管理员账户：
- 用户名：`admin`
- 密码：`admin123`

> 重要：在生产环境中，请在首次登录后立即修改默认密码！

## 常见问题

### 1. 数据库连接失败

**问题**：启动后端服务时出现数据库连接错误。

**解决方案**：
- 确认MySQL服务正在运行
- 检查数据库连接信息（URL、用户名、密码）是否正确
- 确认数据库用户有足够的权限
- 检查防火墙设置是否允许连接到MySQL端口

### 2. 前端无法连接到后端API

**问题**：前端应用无法连接到后端API。

**解决方案**：
- 确认后端服务正在运行
- 检查前端代理配置是否正确
- 在浏览器控制台中查看网络请求错误
- 检查CORS配置是否正确

### 3. 编译错误

**问题**：Maven构建或npm构建失败。

**解决方案**：
- 确保安装了正确版本的JDK和Node.js
- 清除缓存：`mvn clean` 或 `npm cache clean --force`
- 检查依赖项是否可访问
- 查看构建日志以获取详细错误信息

### 4. 内存不足

**问题**：应用启动时出现内存不足错误。

**解决方案**：
- 增加JVM堆内存：`java -Xmx1g -jar cas-0.0.1-SNAPSHOT.jar`
- 检查服务器资源使用情况
- 优化应用配置以减少内存使用

### 5. 会话超时

**问题**：用户会话过快超时。

**解决方案**：
- 在`application.yml`中调整JWT令牌过期时间：
  ```yaml
  jwt:
    expiration: 86400000  # 24小时（毫秒）
  ```

## 联系支持

如有任何问题或需要帮助，请联系：

- 邮箱：<EMAIL>
- 电话：123-456-7890
