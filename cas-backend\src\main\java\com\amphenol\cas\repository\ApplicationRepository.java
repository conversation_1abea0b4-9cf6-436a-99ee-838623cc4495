package com.amphenol.cas.repository;

import com.amphenol.cas.model.Application;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ApplicationRepository extends JpaRepository<Application, Long> {
    Optional<Application> findByClientId(String clientId);
    
    Boolean existsByName(String name);
    
    Boolean existsByClientId(String clientId);
}
