package com.amphenol.cas.controller;

import com.amphenol.cas.model.ERole;
import com.amphenol.cas.model.Role;
import com.amphenol.cas.model.User;
import com.amphenol.cas.payload.request.RoleRequest;
import com.amphenol.cas.payload.response.MessageResponse;
import com.amphenol.cas.repository.RoleRepository;
import com.amphenol.cas.repository.UserRepository;
import com.amphenol.cas.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/roles")
public class RoleController {
    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuditLogService auditLogService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<Role> getAllRoles() {
        return roleRepository.findAll();
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Role> getRoleById(@PathVariable Long id) {
        return roleRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> createRole(@Valid @RequestBody RoleRequest roleRequest) {
        try {
            // 创建新角色
            Role role = new Role();

            // 尝试将角色名称转换为枚举
            try {
                // 如果名称是USER、ADMIN或SUPER_ADMIN，则使用枚举
                ERole eRole = ERole.valueOf("ROLE_" + roleRequest.getName().toUpperCase());
                role.setName(eRole);
            } catch (IllegalArgumentException e) {
                // 如果名称不是预定义的枚举值，则使用ROLE_USER作为默认值
                role.setName(ERole.ROLE_USER);
            }

            // 设置描述为自定义名称
            role.setDescription(roleRequest.getName());

            // 检查角色名称长度
            if (roleRequest.getName().length() < 3 || roleRequest.getName().length() > 50) {
                return ResponseEntity.badRequest()
                        .body(new MessageResponse("Error: Role name size must be between 3 and 50 characters!"));
            }

            // 检查角色描述是否已存在
            if (roleRepository.findAll().stream()
                    .anyMatch(r -> r.getDescription() != null &&
                              r.getDescription().equalsIgnoreCase(roleRequest.getName()))) {
                return ResponseEntity.badRequest()
                        .body(new MessageResponse("Error: Role name is already taken!"));
            }

            role = roleRepository.save(role);

            // 记录创建角色操作
            auditLogService.logEvent("创建角色", "创建角色: " + role.getDescription());

            // 创建包含ID的响应
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Role created successfully!");
            response.put("id", role.getId());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Failed to create role. " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> updateRole(@PathVariable Long id, @Valid @RequestBody RoleRequest roleRequest) {
        return roleRepository.findById(id)
                .map(role -> {
                    try {
                        // 检查是否是超级管理员角色
                        if (role.getName() == ERole.ROLE_SUPER_ADMIN) {
                            return ResponseEntity.badRequest()
                                    .body(new MessageResponse("Error: Cannot modify Super Admin role!"));
                        }

                        // 检查角色名称长度
                        if (roleRequest.getName().length() < 3 || roleRequest.getName().length() > 50) {
                            return ResponseEntity.badRequest()
                                    .body(new MessageResponse("Error: Role name size must be between 3 and 50 characters!"));
                        }

                        // 检查新名称是否已存在（排除当前角色）
                        if (roleRepository.findAll().stream()
                                .filter(r -> r.getId() != role.getId())
                                .anyMatch(r -> r.getDescription() != null &&
                                          r.getDescription().equalsIgnoreCase(roleRequest.getName()))) {
                            return ResponseEntity.badRequest()
                                    .body(new MessageResponse("Error: Role name is already taken!"));
                        }

                        // 更新角色描述
                        role.setDescription(roleRequest.getName());
                        roleRepository.save(role);

                        // 记录更新角色操作
                        auditLogService.logEvent("更新角色", "更新角色: " + role.getDescription());

                        return ResponseEntity.ok(new MessageResponse("Role updated successfully!"));
                    } catch (Exception e) {
                        return ResponseEntity.badRequest()
                                .body(new MessageResponse("Error: Failed to update role. " + e.getMessage()));
                    }
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> deleteRole(@PathVariable Long id, @RequestParam(required = false) Boolean force) {
        return roleRepository.findById(id)
                .map(role -> {
                    // 检查是否是超级管理员角色
                    if (role.getName() == ERole.ROLE_SUPER_ADMIN) {
                        return ResponseEntity.badRequest()
                                .body(new MessageResponse("Error: Cannot delete Super Admin role!"));
                    }

                    // 检查是否有用户关联此角色
                    List<User> usersWithRole = userRepository.findAll().stream()
                            .filter(user -> user.getRoles().contains(role))
                            .collect(Collectors.toList());

                    if (!usersWithRole.isEmpty() && (force == null || !force)) {
                        return ResponseEntity.badRequest()
                                .body(new MessageResponse("Warning: This role is assigned to " +
                                        usersWithRole.size() + " user(s). Set force=true to confirm deletion."));
                    }

                    // 删除角色
                    String roleName = role.getDescription() != null ? role.getDescription() : role.getName().toString();
                    roleRepository.delete(role);

                    // 记录删除角色操作
                    auditLogService.logEvent("删除角色", "删除角色: " + roleName);

                    return ResponseEntity.ok(new MessageResponse("Role deleted successfully!"));
                })
                .orElse(ResponseEntity.notFound().build());
    }



    @PostMapping("/assign")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> assignRoleToUser(@RequestParam Long userId, @RequestParam Long roleId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("Error: User not found."));

        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RuntimeException("Error: Role not found."));

        Set<Role> userRoles = user.getRoles();
        userRoles.add(role);
        user.setRoles(userRoles);
        userRepository.save(user);

        // 记录分配角色操作
        auditLogService.logEvent("分配角色", "用户 " + user.getUsername() + " 被分配角色: " + role.getDescription());

        return ResponseEntity.ok(new MessageResponse("Role assigned successfully!"));
    }

    @PostMapping("/remove")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> removeRoleFromUser(@RequestParam Long userId, @RequestParam Long roleId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("Error: User not found."));

        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RuntimeException("Error: Role not found."));

        Set<Role> userRoles = user.getRoles();
        userRoles.remove(role);
        user.setRoles(userRoles);
        userRepository.save(user);

        // 记录移除角色操作
        auditLogService.logEvent("移除角色", "用户 " + user.getUsername() + " 被移除角色: " + role.getDescription());

        return ResponseEntity.ok(new MessageResponse("Role removed successfully!"));
    }

    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Set<Role>> getUserRoles(@PathVariable Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("Error: User not found."));

        return ResponseEntity.ok(user.getRoles());
    }

    @GetMapping("/{roleId}/users")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<List<User>> getUsersByRole(@PathVariable Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RuntimeException("Error: Role not found."));

        List<User> usersWithRole = userRepository.findAll().stream()
                .filter(user -> user.getRoles().contains(role))
                .collect(Collectors.toList());

        return ResponseEntity.ok(usersWithRole);
    }


}
