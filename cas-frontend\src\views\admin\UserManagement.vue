<template>
  <div class="user-management">
    <h1>用户管理</h1>
    
    <el-card class="user-table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <el-button type="primary" @click="showAddUserDialog">添加用户</el-button>
        </div>
      </template>
      
      <el-table :data="users" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
        <el-table-column prop="fullName" label="姓名" width="120"></el-table-column>
        <el-table-column prop="phoneNumber" label="电话" width="120"></el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="角色" width="200">
          <template #default="scope">
            <el-tag v-for="role in scope.row.roles" :key="role.name" style="margin-right: 5px;">
              {{ formatRole(role.name) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="confirmDeleteUser(scope.row)" 
                      v-if="isSuperAdmin && scope.row.username !== 'admin'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 编辑用户对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
      <el-form :model="userForm" label-width="100px" :rules="rules" ref="userForm">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="editMode"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editMode">
          <el-input v-model="userForm.password" type="password"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="fullName">
          <el-input v-model="userForm.fullName"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phoneNumber">
          <el-input v-model="userForm.phoneNumber"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="userForm.enabled"></el-switch>
        </el-form-item>
        <el-form-item label="角色" prop="roles">
          <el-checkbox-group v-model="userForm.roles">
            <el-checkbox label="ROLE_USER">普通用户</el-checkbox>
            <el-checkbox label="ROLE_ADMIN">管理员</el-checkbox>
            <el-checkbox label="ROLE_SUPER_ADMIN" v-if="isSuperAdmin">超级管理员</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'UserManagement',
  data() {
    return {
      users: [],
      loading: false,
      dialogVisible: false,
      editMode: false,
      dialogTitle: '添加用户',
      userForm: {
        username: '',
        email: '',
        password: '',
        fullName: '',
        phoneNumber: '',
        enabled: true,
        roles: ['ROLE_USER']
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 40, message: '长度在 6 到 40 个字符', trigger: 'blur' }
        ],
        fullName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        roles: [
          { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    },
    isSuperAdmin() {
      return this.currentUser && this.currentUser.roles && 
             this.currentUser.roles.includes('ROLE_SUPER_ADMIN');
    }
  },
  created() {
    this.loadUsers();
  },
  methods: {
    loadUsers() {
      this.loading = true;
      UserService.getUsers()
        .then(response => {
          this.users = response.data;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error loading users:', error);
          this.$message.error('加载用户列表失败');
          this.loading = false;
        });
    },
    showAddUserDialog() {
      this.editMode = false;
      this.dialogTitle = '添加用户';
      this.userForm = {
        username: '',
        email: '',
        password: '',
        fullName: '',
        phoneNumber: '',
        enabled: true,
        roles: ['ROLE_USER']
      };
      this.dialogVisible = true;
    },
    editUser(user) {
      this.editMode = true;
      this.dialogTitle = '编辑用户';
      this.userForm = {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        phoneNumber: user.phoneNumber,
        enabled: user.enabled,
        roles: user.roles.map(role => role.name)
      };
      this.dialogVisible = true;
    },
    saveUser() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.editMode) {
            UserService.updateUser(this.userForm.id, this.userForm)
              .then(() => {
                this.$message.success('用户更新成功');
                this.dialogVisible = false;
                this.loadUsers();
              })
              .catch(error => {
                console.error('Error updating user:', error);
                this.$message.error('更新用户失败: ' + (error.response?.data?.message || error.message));
                this.loading = false;
              });
          } else {
            // 注册新用户
            this.$store.dispatch('auth/register', this.userForm)
              .then(() => {
                this.$message.success('用户创建成功');
                this.dialogVisible = false;
                this.loadUsers();
              })
              .catch(error => {
                console.error('Error creating user:', error);
                this.$message.error('创建用户失败: ' + (error.response?.data?.message || error.message));
                this.loading = false;
              });
          }
        }
      });
    },
    confirmDeleteUser(user) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUser(user.id);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    deleteUser(userId) {
      this.loading = true;
      UserService.deleteUser(userId)
        .then(() => {
          this.$message.success('用户删除成功');
          this.loadUsers();
        })
        .catch(error => {
          console.error('Error deleting user:', error);
          this.$message.error('删除用户失败: ' + (error.response?.data?.message || error.message));
          this.loading = false;
        });
    },
    formatRole(role) {
      switch(role) {
        case 'ROLE_USER': return '普通用户';
        case 'ROLE_ADMIN': return '管理员';
        case 'ROLE_SUPER_ADMIN': return '超级管理员';
        default: return role;
      }
    }
  }
};
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.user-table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
