<template>
  <div class="application-management">
    <h1>应用管理</h1>

    <el-card class="app-table-card">
      <template #header>
        <div class="card-header">
          <span>应用列表</span>
          <el-button type="primary" @click="showAddAppDialog">添加应用</el-button>
        </div>
      </template>

      <el-table :data="applications" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="应用名称" width="150"></el-table-column>
        <el-table-column prop="description" label="描述" width="200"></el-table-column>
        <el-table-column prop="redirectUrl" label="回调URL" width="200"></el-table-column>
        <el-table-column prop="clientId" label="Client ID" width="200"></el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="editApp(scope.row)">编辑</el-button>
            <el-button size="small" type="warning" @click="regenerateSecret(scope.row)">重置密钥</el-button>
            <el-button size="small" type="danger" @click="confirmDeleteApp(scope.row)"
                      v-if="isSuperAdmin">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑应用对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
      <el-form :model="appForm" label-width="120px" :rules="rules" ref="appForm">
        <el-form-item label="应用名称" prop="name">
          <el-input v-model="appForm.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="appForm.description" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="回调URL" prop="redirectUrl">
          <el-input v-model="appForm.redirectUrl"></el-input>
        </el-form-item>
        <el-form-item label="Client ID" prop="clientId" v-if="editMode">
          <el-input v-model="appForm.clientId" disabled></el-input>
        </el-form-item>
        <el-form-item label="Client Secret" prop="clientSecret" v-if="editMode">
          <el-input v-model="appForm.clientSecret" disabled>
            <template #append>
              <el-button @click="copyToClipboard(appForm.clientSecret)">复制</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="appForm.enabled"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveApp">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'ApplicationManagement',
  data() {
    return {
      applications: [],
      loading: false,
      dialogVisible: false,
      editMode: false,
      dialogTitle: '添加应用',
      appForm: {
        name: '',
        description: '',
        redirectUrl: '',
        clientId: '',
        clientSecret: '',
        enabled: true
      },
      rules: {
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入应用描述', trigger: 'blur' }
        ],
        redirectUrl: [
          { required: true, message: '请输入回调URL', trigger: 'blur' },
          { pattern: /^https?:\/\//, message: '回调URL必须以http://或https://开头', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    },
    isSuperAdmin() {
      return this.currentUser && this.currentUser.roles &&
             this.currentUser.roles.includes('ROLE_SUPER_ADMIN');
    }
  },
  created() {
    this.loadApplications();
  },
  methods: {
    loadApplications() {
      this.loading = true;
      UserService.getApplications()
        .then(response => {
          this.applications = response.data;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error loading applications:', error);
          this.$message.error('加载应用列表失败');
          this.loading = false;
        });
    },
    showAddAppDialog() {
      this.editMode = false;
      this.dialogTitle = '添加应用';
      this.appForm = {
        name: '',
        description: '',
        redirectUrl: '',
        enabled: true
      };
      this.dialogVisible = true;
    },
    editApp(app) {
      this.editMode = true;
      this.dialogTitle = '编辑应用';
      this.appForm = {
        id: app.id,
        name: app.name,
        description: app.description,
        redirectUrl: app.redirectUrl,
        clientId: app.clientId,
        clientSecret: app.clientSecret,
        enabled: app.enabled
      };
      this.dialogVisible = true;
    },
    saveApp() {
      this.$refs.appForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.editMode) {
            UserService.updateApplication(this.appForm.id, this.appForm)
              .then(() => {
                this.$message.success('应用更新成功');
                this.dialogVisible = false;
                this.loadApplications();
              })
              .catch(error => {
                console.error('Error updating application:', error);
                this.$message.error('更新应用失败: ' + (error.response?.data?.message || error.message));
                this.loading = false;
              });
          } else {
            UserService.createApplication(this.appForm)
              .then(response => {
                this.$message.success('应用创建成功');
                this.dialogVisible = false;
                this.loadApplications();

                // 显示新创建的应用的Client ID和Secret
                this.$alert(`请保存以下信息：<br>
                  <strong>Client ID:</strong> ${response.data.clientId}<br>
                  <strong>Client Secret:</strong> ${response.data.clientSecret}`,
                  '应用凭据', {
                  dangerouslyUseHTMLString: true,
                  confirmButtonText: '我已保存'
                });
              })
              .catch(error => {
                console.error('Error creating application:', error);
                this.$message.error('创建应用失败: ' + (error.response?.data?.message || error.message));
                this.loading = false;
              });
          }
        }
      });
    },
    confirmDeleteApp(app) {
      this.$confirm('此操作将永久删除该应用, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteApp(app.id);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    deleteApp(appId) {
      this.loading = true;
      UserService.deleteApplication(appId)
        .then(() => {
          this.$message.success('应用删除成功');
          this.loadApplications();
        })
        .catch(error => {
          console.error('Error deleting application:', error);
          this.$message.error('删除应用失败: ' + (error.response?.data?.message || error.message));
          this.loading = false;
        });
    },
    regenerateSecret(app) {
      this.$confirm('此操作将重新生成Client Secret, 旧的密钥将失效, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        UserService.regenerateClientSecret(app.id)
          .then(response => {
            this.$message.success('Client Secret重置成功');
            this.loadApplications();

            // 显示新的Secret
            this.$alert(`新的Client Secret: <br><strong>${response.data.clientSecret}</strong>`,
              '新凭据', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '我已保存'
            });
          })
          .catch(error => {
            console.error('Error regenerating client secret:', error);
            this.$message.error('重置Client Secret失败: ' + (error.response?.data?.message || error.message));
            this.loading = false;
          });
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },
    copyToClipboard(text) {
      navigator.clipboard.writeText(text).then(() => {
        this.$message.success('已复制到剪贴板');
      }).catch(err => {
        console.error('无法复制文本: ', err);
        this.$message.error('复制失败');
      });
    }
  }
};
</script>

<style scoped>
.application-management {
  padding: 20px;
}

.app-table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
