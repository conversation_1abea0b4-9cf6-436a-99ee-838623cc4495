package com.amphenol.cas.controller;

import com.amphenol.cas.model.Application;
import com.amphenol.cas.payload.response.MessageResponse;
import com.amphenol.cas.repository.ApplicationRepository;
import com.amphenol.cas.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/applications")
public class ApplicationController {
    @Autowired
    ApplicationRepository applicationRepository;

    @Autowired
    AuditLogService auditLogService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<Application> getAllApplications() {
        return applicationRepository.findAll();
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Application> getApplicationById(@PathVariable Long id) {
        return applicationRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> createApplication(@Valid @RequestBody Application application) {
        if (applicationRepository.existsByName(application.getName())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Application name is already taken!"));
        }

        if (applicationRepository.existsByClientId(application.getClientId())) {
            return ResponseEntity
                    .badRequest()
                    .body(new MessageResponse("Error: Client ID is already in use!"));
        }

        // Generate client ID and secret if not provided
        if (application.getClientId() == null || application.getClientId().trim().isEmpty()) {
            application.setClientId(UUID.randomUUID().toString());
        }

        if (application.getClientSecret() == null || application.getClientSecret().trim().isEmpty()) {
            application.setClientSecret(UUID.randomUUID().toString());
        }

        Application savedApplication = applicationRepository.save(application);

        // 记录创建应用操作
        auditLogService.logEvent("创建应用", "创建应用: " + application.getName());

        return ResponseEntity.ok(savedApplication);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> updateApplication(@PathVariable Long id, @Valid @RequestBody Application applicationDetails) {
        return applicationRepository.findById(id)
                .map(application -> {
                    application.setName(applicationDetails.getName());
                    application.setDescription(applicationDetails.getDescription());
                    application.setRedirectUrl(applicationDetails.getRedirectUrl());
                    application.setEnabled(applicationDetails.isEnabled());

                    // 记录更新应用操作
                    auditLogService.logEvent("更新应用", "更新应用: " + application.getName());

                    return ResponseEntity.ok(applicationRepository.save(application));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> deleteApplication(@PathVariable Long id) {
        return applicationRepository.findById(id)
                .map(application -> {
                    // 记录删除应用操作
                    auditLogService.logEvent("删除应用", "删除应用: " + application.getName());

                    applicationRepository.delete(application);
                    return ResponseEntity.ok().build();
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/{id}/regenerate-secret")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> regenerateClientSecret(@PathVariable Long id) {
        return applicationRepository.findById(id)
                .map(application -> {
                    application.setClientSecret(UUID.randomUUID().toString());

                    // 记录重新生成密钥操作
                    auditLogService.logEvent("重新生成密钥", "应用 " + application.getName() + " 的密钥被重新生成");

                    return ResponseEntity.ok(applicationRepository.save(application));
                })
                .orElse(ResponseEntity.notFound().build());
    }
}
