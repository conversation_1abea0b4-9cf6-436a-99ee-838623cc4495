import axios from 'axios';

const API_URL = '/api/auth/'; // 使用相对路径，axios.defaults.baseURL 已设置为 http://localhost:8080

class AuthService {
  login(user) {
    console.log('Login attempt for user:', user.username);
    return axios
      .post(API_URL + 'signin', {
        username: user.username,
        password: user.password
      })
      .then(response => {
        console.log('Login response:', response.data);
        if (response.data.token) {
          console.log('Token received, storing in localStorage');
          localStorage.setItem('user', JSON.stringify(response.data));
        } else {
          console.warn('No token received in login response');
        }

        return response.data;
      })
      .catch(error => {
        console.error('Login error:', error.response ? error.response.data : error.message);
        console.error('Full error object:', error);
        if (error.response) {
          console.error('Response status:', error.response.status);
          console.error('Response headers:', error.response.headers);
          console.error('Response data:', error.response.data);
        }
        throw error;
      });
  }

  logout() {
    // 获取当前用户名
    const user = JSON.parse(localStorage.getItem('user'));
    const username = user ? user.username : null;

    // 调用后端退出登录API
    if (username) {
      axios.post(API_URL + 'signout', null, {
        params: { username },
        headers: { 'Authorization': 'Bearer ' + user.token }
      }).catch(error => {
        console.error('Error logging out:', error);
      });
    }

    // 清除本地存储
    localStorage.removeItem('user');
  }

  register(user) {
    return axios.post(API_URL + 'signup', {
      username: user.username,
      email: user.email,
      password: user.password,
      fullName: user.fullName,
      phoneNumber: user.phoneNumber,
      roles: user.roles
    });
  }
}

export default new AuthService();
