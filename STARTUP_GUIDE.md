# Amphenol CAS System Startup Guide

This document provides detailed steps on how to start and run the Amphenol Central Authentication Service (CAS) system, including the Java backend service and Vue frontend application.

## System Requirements

- JDK 8+
- Maven 3.6+
- Node.js 14+
- npm 6+ or yarn 1.22+
- MySQL 8.0+

## Database Setup

1. Create MySQL database:

```sql
CREATE DATABASE amphenol_cas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Configure database connection in `cas-backend/src/main/resources/application.yml`:

```yaml
spring:
  datasource:
    url: *****************************************************************************************************
    username: root  # Change to your MySQL username
    password: your_password  # Change to your MySQL password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## Backend Service Startup

### Development Environment

1. Navigate to the backend project directory:

```bash
cd cas-backend
```

2. Compile the project with <PERSON><PERSON>:

```bash
mvn clean install
```

3. Start the Spring Boot application:

```bash
mvn spring-boot:run
```

The backend service will run at http://localhost:8080/api.

### Production Environment

1. Build an executable JAR:

```bash
cd cas-backend
mvn clean package -DskipTests
```

2. Copy the generated JAR (located in the `target` directory) to your server.

3. Run on the server:

```bash
java -jar cas-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

## Frontend Application Startup

### Development Environment

1. Navigate to the frontend project directory:

```bash
cd cas-frontend
```

2. Install dependencies:

```bash
# Using npm
npm install

# Or using yarn
yarn install
```

3. Start the development server:

```bash
# Using npm
npm run serve

# Or using yarn
yarn serve
```

The frontend application will run at http://localhost:8081.

### Production Environment

1. Build for production:

```bash
cd cas-frontend

# Using npm
npm run build

# Or using yarn
yarn build
```

2. Deploy the files in the `dist` directory to a web server (e.g., Nginx).

3. Nginx configuration example:

```nginx
server {
    listen 80;
    server_name cas.amphenol.com;  # Replace with your domain

    root /path/to/cas-frontend/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Accessing the System

### Default Admin Account

The system initializes with a default admin account:
- Username: `admin`
- Password: `admin123`

> Important: In production, change the default password immediately after first login!

## Troubleshooting

### 1. Database Connection Failure

**Issue**: Database connection error when starting the backend service.

**Solution**:
- Confirm MySQL service is running
- Check database connection information (URL, username, password)
- Ensure database user has sufficient privileges
- Check firewall settings

### 2. Frontend Cannot Connect to Backend API

**Issue**: Frontend application cannot connect to backend API.

**Solution**:
- Confirm backend service is running
- Check frontend proxy configuration
- Look for network request errors in browser console
- Check CORS configuration

### 3. Compilation Errors

**Issue**: Maven or npm build fails.

**Solution**:
- Ensure correct versions of JDK and Node.js are installed
- Clear cache: `mvn clean` or `npm cache clean --force`
- Check if dependencies are accessible
- Review build logs for detailed error information

## Support Contact

For any questions or assistance, please contact:

- Email: <EMAIL>
- Phone: ************
