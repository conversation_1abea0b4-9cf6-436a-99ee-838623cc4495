package com.amphenol.cas.repository;

import com.amphenol.cas.model.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {
    List<AuditLog> findByUsername(String username, Sort sort);
    List<AuditLog> findByUsername(String username);

    List<AuditLog> findByTimestampBetween(LocalDateTime start, LocalDateTime end, Sort sort);
    List<AuditLog> findByTimestampBetween(LocalDateTime start, LocalDateTime end);

    List<AuditLog> findByAction(String action, Sort sort);
    List<AuditLog> findByAction(String action);
}
