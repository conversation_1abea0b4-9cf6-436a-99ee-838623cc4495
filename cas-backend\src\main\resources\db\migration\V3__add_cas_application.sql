-- 添加统一认证平台应用
INSERT INTO applications (name, description, client_id, client_secret, redirect_url, enabled)
VALUES ('统一认证平台', '用户认证与授权管理系统', 'cas-platform', UUID(), 'http://localhost:8081/login', 1);

-- 获取刚插入的应用ID
SET @cas_app_id = LAST_INSERT_ID();

-- 获取超级管理员角色ID
SET @super_admin_role_id = (SELECT id FROM roles WHERE name = 'ROLE_SUPER_ADMIN');

-- 将应用分配给超级管理员角色
INSERT INTO application_roles (application_id, role_id)
VALUES (@cas_app_id, @super_admin_role_id);