package com.amphenol.cas.controller;

import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;
import java.util.*;

/**
 * AD域组织单元获取器
 * 用于获取Active Directory域中的所有组织单元(OU)
 */
public class ADOUFetcher {

    // AD域连接配置
    private static final String LDAP_URL = "ldap://192.168.110.199:389";
    private static final String DOMAIN = "jet.com";
    private static final String USERNAME = "<EMAIL>";
    private static final String PASSWORD = "Q789+a456+z123.#";
    private static final String BASE_DN = "DC=jet,DC=com";

    private DirContext context;

    /**
     * 建立LDAP连接
     */
    public boolean connect() {
        try {
            Hashtable<String, String> env = new Hashtable<>();
            env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
            env.put(Context.PROVIDER_URL, LDAP_URL);
            env.put(Context.SECURITY_AUTHENTICATION, "simple");
            env.put(Context.SECURITY_PRINCIPAL, USERNAME);
            env.put(Context.SECURITY_CREDENTIALS, PASSWORD);

            // 设置连接超时
            env.put("com.sun.jndi.ldap.connect.timeout", "5000");
            env.put("com.sun.jndi.ldap.read.timeout", "10000");

            context = new InitialDirContext(env);
            System.out.println("成功连接到AD域: " + DOMAIN);
            return true;

        } catch (NamingException e) {
            System.err.println("连接AD域失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取所有组织单元
     */
    public List<OrganizationalUnit> getAllOUs() {
        List<OrganizationalUnit> ouList = new ArrayList<>();

        if (context == null) {
            System.err.println("未建立LDAP连接，请先调用connect()方法");
            return ouList;
        }

        try {
            // 搜索所有组织单元
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);

            // 设置返回的属性
            String[] returnedAttributes = {
                "distinguishedName", "name", "description",
                "ou", "whenCreated", "whenChanged", "objectClass"
            };
            searchControls.setReturningAttributes(returnedAttributes);

            // LDAP过滤器：查找所有组织单元
            String filter = "(objectClass=organizationalUnit)";

            NamingEnumeration<SearchResult> results = context.search(BASE_DN, filter, searchControls);

            while (results.hasMore()) {
                SearchResult result = results.next();
                OrganizationalUnit ou = parseOU(result);
                if (ou != null) {
                    ouList.add(ou);
                }
            }

            results.close();

        } catch (NamingException e) {
            System.err.println("搜索组织单元失败: " + e.getMessage());
            e.printStackTrace();
        }

        return ouList;
    }

    /**
     * 解析搜索结果为组织单元对象
     */
    private OrganizationalUnit parseOU(SearchResult result) {
        try {
            Attributes attributes = result.getAttributes();
            OrganizationalUnit ou = new OrganizationalUnit();

            // 获取DN
            String dn = result.getNameInNamespace();
            ou.setDistinguishedName(dn);

            // 获取名称
            Attribute nameAttr = attributes.get("name");
            if (nameAttr != null) {
                ou.setName((String) nameAttr.get());
            }

            // 获取OU名称
            Attribute ouAttr = attributes.get("ou");
            if (ouAttr != null) {
                ou.setOuName((String) ouAttr.get());
            }

            // 获取描述
            Attribute descAttr = attributes.get("description");
            if (descAttr != null) {
                ou.setDescription((String) descAttr.get());
            }

            // 获取创建时间
            Attribute createdAttr = attributes.get("whenCreated");
            if (createdAttr != null) {
                ou.setWhenCreated((String) createdAttr.get());
            }

            // 获取修改时间
            Attribute changedAttr = attributes.get("whenChanged");
            if (changedAttr != null) {
                ou.setWhenChanged((String) changedAttr.get());
            }

            // 计算层级
            ou.setLevel(calculateLevel(dn));

            return ou;

        } catch (NamingException e) {
            System.err.println("解析组织单元失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 计算组织单元的层级深度
     */
    private int calculateLevel(String dn) {
        if (dn == null || dn.isEmpty()) {
            return 0;
        }

        // 移除基础DN部分
        String relativeDn = dn.replace("," + BASE_DN, "");

        // 计算OU的数量
        int level = 0;
        String[] parts = relativeDn.split(",");
        for (String part : parts) {
            if (part.trim().toUpperCase().startsWith("OU=")) {
                level++;
            }
        }

        return level;
    }

    /**
     * 获取指定OU下的子OU
     */
    public List<OrganizationalUnit> getChildOUs(String parentDN) {
        List<OrganizationalUnit> childOUs = new ArrayList<>();

        if (context == null) {
            System.err.println("未建立LDAP连接，请先调用connect()方法");
            return childOUs;
        }

        try {
            SearchControls searchControls = new SearchControls();
            searchControls.setSearchScope(SearchControls.ONELEVEL_SCOPE);

            String[] returnedAttributes = {
                "distinguishedName", "name", "description",
                "ou", "whenCreated", "whenChanged"
            };
            searchControls.setReturningAttributes(returnedAttributes);

            String filter = "(objectClass=organizationalUnit)";

            NamingEnumeration<SearchResult> results = context.search(parentDN, filter, searchControls);

            while (results.hasMore()) {
                SearchResult result = results.next();
                OrganizationalUnit ou = parseOU(result);
                if (ou != null) {
                    childOUs.add(ou);
                }
            }

            results.close();

        } catch (NamingException e) {
            System.err.println("搜索子组织单元失败: " + e.getMessage());
            e.printStackTrace();
        }

        return childOUs;
    }

    /**
     * 打印组织单元树形结构
     */
    public void printOUTree() {
        List<OrganizationalUnit> allOUs = getAllOUs();

        // 按层级和名称排序
        allOUs.sort((ou1, ou2) -> {
            int levelCompare = Integer.compare(ou1.getLevel(), ou2.getLevel());
            if (levelCompare != 0) {
                return levelCompare;
            }
            return ou1.getName().compareTo(ou2.getName());
        });

        System.out.println("\n=== AD域组织单元树形结构 ===");
        System.out.println("域: " + DOMAIN);
        System.out.println("基础DN: " + BASE_DN);
        System.out.println("总计: " + allOUs.size() + " 个组织单元\n");

        for (OrganizationalUnit ou : allOUs) {
            String indent = "  ".repeat(ou.getLevel());
            System.out.println(indent + "├─ " + ou.getName());
            System.out.println(indent + "   DN: " + ou.getDistinguishedName());
            if (ou.getDescription() != null && !ou.getDescription().isEmpty()) {
                System.out.println(indent + "   描述: " + ou.getDescription());
            }
            System.out.println();
        }
    }

    /**
     * 关闭LDAP连接
     */
    public void close() {
        if (context != null) {
            try {
                context.close();
                System.out.println("LDAP连接已关闭");
            } catch (NamingException e) {
                System.err.println("关闭LDAP连接失败: " + e.getMessage());
            }
        }
    }

    /**
     * 组织单元数据类
     */
    public static class OrganizationalUnit {
        private String distinguishedName;
        private String name;
        private String ouName;
        private String description;
        private String whenCreated;
        private String whenChanged;
        private int level;

        // Getters and Setters
        public String getDistinguishedName() { return distinguishedName; }
        public void setDistinguishedName(String distinguishedName) { this.distinguishedName = distinguishedName; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getOuName() { return ouName; }
        public void setOuName(String ouName) { this.ouName = ouName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getWhenCreated() { return whenCreated; }
        public void setWhenCreated(String whenCreated) { this.whenCreated = whenCreated; }

        public String getWhenChanged() { return whenChanged; }
        public void setWhenChanged(String whenChanged) { this.whenChanged = whenChanged; }

        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }

        @Override
        public String toString() {
            return "OrganizationalUnit{" +
                    "name='" + name + '\'' +
                    ", distinguishedName='" + distinguishedName + '\'' +
                    ", description='" + description + '\'' +
                    ", level=" + level +
                    '}';
        }
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        ADOUFetcher fetcher = new ADOUFetcher();

        try {
            // 连接AD域
            if (fetcher.connect()) {

                // 获取所有组织单元
                List<OrganizationalUnit> ous = fetcher.getAllOUs();
                System.out.println("找到 " + ous.size() + " 个组织单元");

                // 打印树形结构
                fetcher.printOUTree();

                // 示例：获取特定OU的子OU
                if (!ous.isEmpty()) {
                    OrganizationalUnit firstOU = ous.get(0);
                    System.out.println("\n=== " + firstOU.getName() + " 的子组织单元 ===");
                    List<OrganizationalUnit> childOUs = fetcher.getChildOUs(firstOU.getDistinguishedName());
                    for (OrganizationalUnit childOU : childOUs) {
                        System.out.println("  - " + childOU.getName());
                    }
                }

            } else {
                System.err.println("无法连接到AD域");
            }

        } finally {
            // 关闭连接
            fetcher.close();
        }
    }
}
