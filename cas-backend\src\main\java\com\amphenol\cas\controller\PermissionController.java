package com.amphenol.cas.controller;

import com.amphenol.cas.model.Application;
import com.amphenol.cas.model.Role;
import com.amphenol.cas.payload.response.MessageResponse;
import com.amphenol.cas.repository.ApplicationRepository;
import com.amphenol.cas.repository.RoleRepository;
import com.amphenol.cas.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.Set;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/permissions")
public class PermissionController {
    @Autowired
    private ApplicationRepository applicationRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private AuditLogService auditLogService;

    @PostMapping("/assign")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> assignRoleToApplication(@RequestParam Long applicationId, @RequestParam Long roleId) {
        // 检查应用是否存在
        if (!applicationRepository.existsById(applicationId)) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Application not found."));
        }

        // 检查角色是否存在
        if (!roleRepository.existsById(roleId)) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Role not found."));
        }

        Application application = applicationRepository.findById(applicationId).get();
        Role role = roleRepository.findById(roleId).get();

        Set<Role> requiredRoles = application.getRequiredRoles();
        if (requiredRoles == null) {
            requiredRoles = new HashSet<>();
        }
        requiredRoles.add(role);
        application.setRequiredRoles(requiredRoles);
        applicationRepository.save(application);

        // 记录分配角色到应用操作
        auditLogService.logEvent("分配角色到应用", "角色 " + role.getDescription() + " 被分配到应用 " + application.getName());

        return ResponseEntity.ok(new MessageResponse("Role assigned to application successfully!"));
    }

    @PostMapping("/remove")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> removeRoleFromApplication(@RequestParam Long applicationId, @RequestParam Long roleId) {
        // 检查应用是否存在
        if (!applicationRepository.existsById(applicationId)) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Application not found."));
        }

        // 检查角色是否存在
        if (!roleRepository.existsById(roleId)) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Role not found."));
        }

        Application application = applicationRepository.findById(applicationId).get();
        Role role = roleRepository.findById(roleId).get();

        Set<Role> requiredRoles = application.getRequiredRoles();
        if (requiredRoles != null) {
            requiredRoles.remove(role);
            application.setRequiredRoles(requiredRoles);
            applicationRepository.save(application);

            // 记录从应用移除角色操作
            auditLogService.logEvent("从应用移除角色", "角色 " + role.getDescription() + " 从应用 " + application.getName() + " 中移除");
        }

        return ResponseEntity.ok(new MessageResponse("Role removed from application successfully!"));
    }

    @GetMapping("/application/{applicationId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> getApplicationRoles(@PathVariable Long applicationId) {
        return applicationRepository.findById(applicationId)
                .map(application -> ResponseEntity.ok(application.getRequiredRoles()))
                .orElse(ResponseEntity.ok(new HashSet<>())); // 如果应用不存在，返回空集合而不是错误
    }
}
