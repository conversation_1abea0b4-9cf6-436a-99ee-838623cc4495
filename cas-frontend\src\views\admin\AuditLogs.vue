<template>
  <div class="audit-logs">
    <h1>审计日志</h1>

    <el-card class="filter-card">
      <template #header>
        <div class="card-header">
          <span>筛选条件</span>
        </div>
      </template>

      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户名">
          <el-input v-model="filterForm.username" placeholder="输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="filterForm.action" placeholder="选择操作类型" clearable style="width: 200px;">
            <el-option label="用户登录" value="用户登录"></el-option>
            <el-option label="用户退出" value="用户退出"></el-option>
            <el-option label="用户注册" value="用户注册"></el-option>
            <el-option label="更新用户" value="更新用户"></el-option>
            <el-option label="删除用户" value="删除用户"></el-option>
            <el-option label="创建应用" value="创建应用"></el-option>
            <el-option label="更新应用" value="更新应用"></el-option>
            <el-option label="删除应用" value="删除应用"></el-option>
            <el-option label="重新生成密钥" value="重新生成密钥"></el-option>
            <el-option label="分配角色到应用" value="分配角色到应用"></el-option>
            <el-option label="从应用移除角色" value="从应用移除角色"></el-option>
            <el-option label="创建角色" value="创建角色"></el-option>
            <el-option label="更新角色" value="更新角色"></el-option>
            <el-option label="删除角色" value="删除角色"></el-option>
            <el-option label="分配角色" value="分配角色"></el-option>
            <el-option label="移除角色" value="移除角色"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59)
            ]"
            :clearable="true"
            :editable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchLogs">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="logs-table-card">
      <template #header>
        <div class="card-header">
          <span>日志列表</span>
          <el-button type="primary" @click="exportLogs">导出日志</el-button>
        </div>
      </template>

      <el-table :data="logs" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="action" label="操作" width="180"></el-table-column>
        <el-table-column prop="ipAddress" label="IP地址" width="120"></el-table-column>
        <el-table-column prop="details" label="详细信息"></el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalLogs">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'AuditLogs',
  data() {
    return {
      logs: [],
      loading: false,
      currentPage: 1,
      pageSize: 20,
      totalLogs: 0,
      filterForm: {
        username: '',
        action: '',
        dateRange: null
      }
    };
  },
  created() {
    // 检查用户是否已登录
    const user = JSON.parse(localStorage.getItem('user'));
    if (user && user.token) {
      this.loadLogs();
    }
  },
  methods: {
    loadLogs() {
      // 检查用户是否已登录
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user || !user.token) {
        this.loading = false;
        return;
      }

      this.loading = true;
      UserService.getAuditLogs()
        .then(response => {
          this.logs = response.data;
          this.totalLogs = this.logs.length;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error loading audit logs:', error);
          this.$message.error('加载审计日志失败');
          this.loading = false;
        });
    },
    searchLogs() {
      // 检查用户是否已登录
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user || !user.token) {
        this.loading = false;
        return;
      }

      this.loading = true;

      if (this.filterForm.username) {
        UserService.getAuditLogsByUsername(this.filterForm.username)
          .then(response => {
            this.logs = response.data;
            this.applyFilters();
            this.loading = false;
          })
          .catch(error => {
            console.error('Error searching logs by username:', error);
            this.$message.error('搜索日志失败');
            this.loading = false;
          });
      } else if (this.filterForm.action) {
        UserService.getAuditLogsByAction(this.filterForm.action)
          .then(response => {
            this.logs = response.data;
            this.applyFilters();
            this.loading = false;
          })
          .catch(error => {
            console.error('Error searching logs by action:', error);
            this.$message.error('搜索日志失败');
            this.loading = false;
          });
      } else if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        // 确保日期格式正确
        const startDate = this.filterForm.dateRange[0];
        const endDate = this.filterForm.dateRange[1];
        console.log('Date range:', startDate, endDate);

        UserService.getAuditLogsByDateRange(startDate, endDate)
          .then(response => {
            this.logs = response.data;
            this.applyFilters();
            this.loading = false;
          })
          .catch(error => {
            console.error('Error searching logs by date range:', error);
            this.$message.error('搜索日志失败');
            this.loading = false;
          });
      } else {
        this.loadLogs();
      }
    },
    applyFilters() {
      let filteredLogs = [...this.logs];

      if (this.filterForm.username) {
        filteredLogs = filteredLogs.filter(log =>
          log.username.toLowerCase().includes(this.filterForm.username.toLowerCase())
        );
      }

      if (this.filterForm.action) {
        filteredLogs = filteredLogs.filter(log =>
          log.action === this.filterForm.action
        );
      }

      if (this.filterForm.dateRange && this.filterForm.dateRange.length === 2) {
        const startDate = new Date(this.filterForm.dateRange[0]).getTime();
        const endDate = new Date(this.filterForm.dateRange[1]).getTime();

        filteredLogs = filteredLogs.filter(log => {
          const logDate = new Date(log.timestamp).getTime();
          return logDate >= startDate && logDate <= endDate;
        });
      }

      this.logs = filteredLogs;
      this.totalLogs = filteredLogs.length;
      this.currentPage = 1;
    },
    resetFilter() {
      // 检查用户是否已登录
      const user = JSON.parse(localStorage.getItem('user'));
      if (!user || !user.token) {
        return;
      }

      this.filterForm = {
        username: '',
        action: '',
        dateRange: null
      };
      this.loadLogs();
    },
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    exportLogs() {
      // 创建CSV内容
      let csvContent = 'ID,时间,用户名,操作,IP地址,详细信息\n';

      this.logs.forEach(log => {
        csvContent += `${log.id},${this.formatDate(log.timestamp)},"${log.username}","${log.action}","${log.ipAddress}","${log.details}"\n`;
      });

      // 创建Blob对象
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `audit_logs_${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';

      // 添加到DOM并触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};
</script>

<style scoped>
.audit-logs {
  padding: 20px;
}

.filter-card, .logs-table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>

<style>
/* 全局样式，确保下拉菜单有足够的宽度 */
.el-select-dropdown {
  min-width: 200px !important;
}

.el-select-dropdown__item {
  padding-right: 20px !important;
  white-space: normal !important;
  height: auto !important;
  line-height: 1.5 !important;
  padding: 8px 20px !important;
}
</style>
