-- 插入角色数据
INSERT INTO roles (name) VALUES ('ROLE_USER') ON DUPLICATE KEY UPDATE name = VALUES(name);
INSERT INTO roles (name) VALUES ('ROLE_ADMIN') ON DUPLICATE KEY UPDATE name = VALUES(name);
INSERT INTO roles (name) VALUES ('ROLE_SUPER_ADMIN') ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入管理员用户（密码：admin123，使用BCrypt加密）
INSERT INTO users (username, email, password, full_name, enabled, account_non_expired, account_non_locked, credentials_non_expired)
VALUES ('admin', '<EMAIL>', '$2a$10$FXMpU.vXsVYjsy7uf3Z1CuwCmwDqF1kKDRIH0exisknZsyVtK.U/O', 'System Administrator', true, true, true, true)
ON DUPLICATE KEY UPDATE username = VALUES(username);

-- 为管理员用户分配超级管理员角色
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id FROM users u, roles r
WHERE u.username = 'admin' AND r.name = 'ROLE_SUPER_ADMIN'
ON DUPLICATE KEY UPDATE user_id = VALUES(user_id), role_id = VALUES(role_id);
