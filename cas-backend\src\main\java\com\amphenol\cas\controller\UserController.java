package com.amphenol.cas.controller;

import com.amphenol.cas.model.Application;
import com.amphenol.cas.model.Role;
import com.amphenol.cas.model.User;
import com.amphenol.cas.repository.ApplicationRepository;
import com.amphenol.cas.repository.UserRepository;
import com.amphenol.cas.service.AuditLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    UserRepository userRepository;

    @Autowired
    ApplicationRepository applicationRepository;

    @Autowired
    AuditLogService auditLogService;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or authentication.principal.id == #id")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        return userRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or authentication.principal.id == #id")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody User userDetails) {
        return userRepository.findById(id)
                .map(user -> {
                    user.setFullName(userDetails.getFullName());
                    user.setEmail(userDetails.getEmail());
                    user.setPhoneNumber(userDetails.getPhoneNumber());
                    user.setEnabled(userDetails.isEnabled());
                    user.setAccountNonExpired(userDetails.isAccountNonExpired());
                    user.setAccountNonLocked(userDetails.isAccountNonLocked());
                    user.setCredentialsNonExpired(userDetails.isCredentialsNonExpired());

                    // 记录用户更新操作
                    auditLogService.logEvent("更新用户", "用户 " + user.getUsername() + " 信息被更新");

                    return ResponseEntity.ok(userRepository.save(user));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        return userRepository.findById(id)
                .map(user -> {
                    // 记录删除用户操作
                    auditLogService.logEvent("删除用户", "用户 " + user.getUsername() + " 被删除");

                    userRepository.delete(user);
                    return ResponseEntity.ok().build();
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/applications")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<Application>> getUserApplications() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("Error: User not found."));

        // 获取用户的所有角色
        Set<Role> userRoles = user.getRoles();

        // 获取这些角色可以访问的所有应用
        Set<Application> userApplications = new HashSet<>();
        for (Role role : userRoles) {
            userApplications.addAll(role.getApplications());
        }

        return ResponseEntity.ok(new ArrayList<>(userApplications));
    }
}
