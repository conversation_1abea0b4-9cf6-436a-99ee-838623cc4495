<template>
  <div class="role-management">
    <h1>角色管理</h1>

    <el-card class="role-table-card">
      <template #header>
        <div class="card-header">
          <span>角色列表</span>
          <div>
            <el-button type="success" @click="showCreateRoleDialog" v-if="isSuperAdmin">新建角色</el-button>
          </div>
        </div>
      </template>

      <el-table :data="roles" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="description" label="角色名称" width="150">
          <template #default="scope">
            {{ scope.row.description || formatRole(scope.row.name) }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <template v-if="scope.row.name !== 'ROLE_SUPER_ADMIN'">
              <el-button size="small" @click="showAssignUserDialog(scope.row)">分配用户</el-button>
              <el-button size="small" @click="showAssignAppDialog(scope.row)">分配应用</el-button>
              <el-button size="small" type="primary" @click="showEditRoleDialog(scope.row)" v-if="isSuperAdmin">编辑</el-button>
              <el-button
                size="small"
                type="danger"
                @click="confirmDeleteRole(scope.row)"
                v-if="isSuperAdmin">
                删除
              </el-button>
            </template>
            <template v-else>
              <span class="super-admin-note">超级管理员角色不可修改</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分配用户对话框 -->
    <el-dialog title="分配用户" v-model="assignUserDialogVisible" width="800px">
      <div class="dialog-header">
        <span>角色: {{ assignUserForm.roleName }}</span>
      </div>

      <el-tabs type="border-card" style="margin-top: 15px;">
        <el-tab-pane label="可选用户">
          <div class="user-search">
            <el-input
              v-model="userSearchKeyword"
              placeholder="搜索用户名"
              prefix-icon="el-icon-search"
              clearable
              @clear="userSearchKeyword = ''"
            ></el-input>
          </div>

          <el-table
            :data="filteredAvailableUsers"
            height="300"
            border
            style="width: 100%"
            v-loading="loadingUsers"
          >
            <el-table-column prop="username" label="用户名"></el-table-column>
            <el-table-column prop="fullName" label="姓名"></el-table-column>
            <el-table-column prop="email" label="邮箱"></el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="addUserToRole(scope.row)"
                  :disabled="isUserAssigned(scope.row.id)"
                >添加</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="已选用户">
          <el-table
            :data="assignedUsers"
            height="300"
            border
            style="width: 100%"
            v-loading="loadingUsers"
          >
            <el-table-column prop="username" label="用户名"></el-table-column>
            <el-table-column prop="fullName" label="姓名"></el-table-column>
            <el-table-column prop="email" label="邮箱"></el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="mini"
                  type="danger"
                  @click="removeUserFromRole(scope.row)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignUserDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 分配应用对话框 -->
    <el-dialog title="分配应用" v-model="assignAppDialogVisible" width="800px">
      <div class="dialog-header">
        <span>角色: {{ assignAppForm.roleName }}</span>
      </div>

      <el-tabs type="border-card" style="margin-top: 15px;">
        <el-tab-pane label="可选应用">
          <div class="app-search">
            <el-input
              v-model="appSearchKeyword"
              placeholder="搜索应用名称"
              prefix-icon="el-icon-search"
              clearable
              @clear="appSearchKeyword = ''"
            ></el-input>
          </div>

          <el-table
            :data="filteredAvailableApps"
            height="300"
            border
            style="width: 100%"
            v-loading="loadingApps"
          >
            <el-table-column prop="name" label="应用名称"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>
            <el-table-column prop="clientId" label="客户端ID"></el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="addAppToRole(scope.row)"
                  :disabled="isAppAssigned(scope.row.id)"
                >添加</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="已选应用">
          <el-table
            :data="assignedApps"
            height="300"
            border
            style="width: 100%"
            v-loading="loadingApps"
          >
            <el-table-column prop="name" label="应用名称"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>
            <el-table-column prop="clientId" label="客户端ID"></el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="mini"
                  type="danger"
                  @click="removeAppFromRole(scope.row)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignAppDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 创建角色对话框 -->
    <el-dialog title="创建角色" v-model="createRoleDialogVisible" width="500px">
      <el-form :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createRoleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createRole">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑角色对话框 -->
    <el-dialog title="编辑角色" v-model="editRoleDialogVisible" width="500px">
      <el-form :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" :disabled="roleForm.originalName === 'ROLE_SUPER_ADMIN'"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editRoleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateRole">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'RoleManagement',
  data() {
    return {
      roles: [],
      users: [],
      applications: [],
      loading: false,
      assignUserDialogVisible: false,
      assignAppDialogVisible: false,
      createRoleDialogVisible: false,
      editRoleDialogVisible: false,
      assignUserForm: {
        roleId: null,
        roleName: '',
        userId: null
      },
      userSearchKeyword: '',
      loadingUsers: false,
      assignedUsers: [],
      assignAppForm: {
        roleId: null,
        roleName: '',
        appId: null
      },
      appSearchKeyword: '',
      loadingApps: false,
      assignedApps: [],
      roleForm: {
        id: null,
        name: '',
        originalName: ''
      },
      roleRules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 3, max: 50, message: '角色名称长度必须在3到50个字符之间', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    },
    isSuperAdmin() {
      return this.currentUser && this.currentUser.roles &&
             this.currentUser.roles.includes('ROLE_SUPER_ADMIN');
    },
    filteredAvailableUsers() {
      if (!this.users) return [];

      // 过滤掉已分配的用户
      const availableUsers = this.users.filter(user =>
        !this.assignedUsers.some(assignedUser => assignedUser.id === user.id)
      );

      // 如果有搜索关键字，进一步过滤
      if (this.userSearchKeyword) {
        const keyword = this.userSearchKeyword.toLowerCase();
        return availableUsers.filter(user =>
          user.username.toLowerCase().includes(keyword) ||
          (user.fullName && user.fullName.toLowerCase().includes(keyword)) ||
          (user.email && user.email.toLowerCase().includes(keyword))
        );
      }

      return availableUsers;
    },
    filteredAvailableApps() {
      if (!this.applications) return [];

      // 过滤掉已分配的应用
      const availableApps = this.applications.filter(app =>
        !this.assignedApps.some(assignedApp => assignedApp.id === app.id)
      );

      // 如果有搜索关键字，进一步过滤
      if (this.appSearchKeyword) {
        const keyword = this.appSearchKeyword.toLowerCase();
        return availableApps.filter(app =>
          app.name.toLowerCase().includes(keyword) ||
          (app.description && app.description.toLowerCase().includes(keyword)) ||
          (app.clientId && app.clientId.toLowerCase().includes(keyword))
        );
      }

      return availableApps;
    }
  },
  created() {
    this.loadRoles();
    this.loadUsers();
    this.loadApplications();
  },
  methods: {
    loadRoles() {
      this.loading = true;
      UserService.getRoles()
        .then(response => {
          this.roles = response.data;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error loading roles:', error);
          this.$message.error('加载角色列表失败');
          this.loading = false;
        });
    },
    loadUsers() {
      UserService.getUsers()
        .then(response => {
          this.users = response.data;
        })
        .catch(error => {
          console.error('Error loading users:', error);
          this.$message.error('加载用户列表失败');
        });
    },
    loadApplications() {
      UserService.getApplications()
        .then(response => {
          this.applications = response.data;
        })
        .catch(error => {
          console.error('Error loading applications:', error);
          this.$message.error('加载应用列表失败');
        });
    },

    showCreateRoleDialog() {
      this.roleForm = {
        id: null,
        name: '',
        originalName: ''
      };
      this.createRoleDialogVisible = true;
    },
    showEditRoleDialog(role) {
      // 超级管理员角色不允许编辑
      if (role.name === 'ROLE_SUPER_ADMIN') {
        this.$message.warning('超级管理员角色不可修改');
        return;
      }

      // 直接设置角色信息，不获取应用列表
      this.roleForm = {
        id: role.id,
        name: role.description || role.name,
        originalName: role.name
      };
      this.editRoleDialogVisible = true;
    },
    createRole() {
      this.$refs.roleFormRef.validate(valid => {
        if (valid) {
          this.loading = true;

          // 创建角色
          UserService.createRole({ name: this.roleForm.name })
            .then(response => {
              this.$message.success(response.data.message || '角色创建成功');
              this.createRoleDialogVisible = false;
              this.loadRoles();
            })
            .catch(error => {
              console.error('Error creating role:', error);
              let errorMsg = '角色创建失败';

              // 处理特定的错误消息
              if (error.response?.data?.message) {
                const message = error.response.data.message;
                if (message.includes('Role name is already taken')) {
                  errorMsg = '角色名称已存在，请使用其他名称';
                } else if (message.includes('size must be between 3 and 50')) {
                  errorMsg = '角色名称长度必须在3到50个字符之间';
                } else {
                  errorMsg += ': ' + message;
                }
              } else if (error.message) {
                errorMsg += ': ' + error.message;
              }

              this.$message.error(errorMsg);
              this.loading = false;
            });
        }
      });
    },
    updateRole() {
      this.$refs.roleFormRef.validate(valid => {
        if (valid) {
          this.loading = true;

          // 只更新角色名称
          UserService.updateRole(this.roleForm.id, { name: this.roleForm.name })
            .then(response => {
              this.$message.success(response.data.message || '角色更新成功');
              this.editRoleDialogVisible = false;
              this.loadRoles();
            })
            .catch(error => {
              console.error('Error updating role:', error);
              let errorMsg = '角色更新失败';

              // 处理特定的错误消息
              if (error.response?.data?.message) {
                const message = error.response.data.message;
                if (message.includes('Role name is already taken')) {
                  errorMsg = '角色名称已存在，请使用其他名称';
                } else if (message.includes('size must be between 3 and 50')) {
                  errorMsg = '角色名称长度必须在3到50个字符之间';
                } else if (message.includes('Cannot modify Super Admin role')) {
                  errorMsg = '超级管理员角色不可修改';
                } else {
                  errorMsg += ': ' + message;
                }
              } else if (error.message) {
                errorMsg += ': ' + error.message;
              }

              this.$message.error(errorMsg);
              this.loading = false;
            });
        }
      });
    },
    confirmDeleteRole(role) {
      this.$confirm(`确定要删除角色 "${this.formatRole(role.name)}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteRole(role.id);
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    deleteRole(id, force = false) {
      this.loading = true;
      UserService.deleteRole(id, force)
        .then(response => {
          this.$message.success(response.data.message || '角色删除成功');
          this.loadRoles();
        })
        .catch(error => {
          console.error('Error deleting role:', error);
          const errorMessage = error.response?.data?.message || error.message;

          // 检查是否是因为角色有关联用户而无法删除
          if (errorMessage.includes('Warning: This role is assigned to')) {
            this.$confirm(errorMessage + ' 是否仍要删除?', '警告', {
              confirmButtonText: '确定删除',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.deleteRole(id, true);
            }).catch(() => {
              this.$message.info('已取消删除');
              this.loading = false;
            });
          } else {
            this.$message.error('角色删除失败: ' + errorMessage);
            this.loading = false;
          }
        });
    },
    showAssignUserDialog(role) {
      // 超级管理员角色不允许分配用户
      if (role.name === 'ROLE_SUPER_ADMIN') {
        this.$message.warning('超级管理员角色不可修改');
        return;
      }

      this.assignUserForm.roleId = role.id;
      this.assignUserForm.roleName = role.description || this.formatRole(role.name);
      this.assignUserForm.userId = null;
      this.userSearchKeyword = '';
      this.loadingUsers = true;
      this.assignedUsers = [];

      // 加载角色已分配的用户
      UserService.getUsersByRole(role.id)
        .then(response => {
          this.assignedUsers = response.data || [];
          this.loadingUsers = false;
        })
        .catch(error => {
          console.error('Error loading users for role:', error);
          this.$message.error('加载角色用户失败: ' + (error.response?.data?.message || error.message));
          this.loadingUsers = false;
        });

      this.assignUserDialogVisible = true;
    },
    showAssignAppDialog(role) {
      // 超级管理员角色不允许分配应用
      if (role.name === 'ROLE_SUPER_ADMIN') {
        this.$message.warning('超级管理员角色不可修改');
        return;
      }

      this.assignAppForm.roleId = role.id;
      this.assignAppForm.roleName = role.description || this.formatRole(role.name);
      this.assignAppForm.appId = null;
      this.appSearchKeyword = '';
      this.loadingApps = true;
      this.assignedApps = [];

      // 加载角色已分配的应用
      UserService.getApplicationRoles(role.id)
        .then(response => {
          this.assignedApps = response.data || [];
          this.loadingApps = false;
          this.assignAppDialogVisible = true;
        })
        .catch(error => {
          console.error('Error loading applications for role:', error);
          // 不显示错误消息，只是使用空应用列表
          this.loadingApps = false;
          this.assignAppDialogVisible = true;
        });
    },
    // 检查用户是否已分配
    isUserAssigned(userId) {
      return this.assignedUsers.some(user => user.id === userId);
    },

    // 添加用户到角色
    addUserToRole(user) {
      this.loadingUsers = true;
      UserService.assignRoleToUser(user.id, this.assignUserForm.roleId)
        .then(response => {
          this.$message.success('用户添加成功');
          // 将用户添加到已分配列表
          this.assignedUsers.push(user);
          this.loadingUsers = false;
        })
        .catch(error => {
          console.error('Error assigning role to user:', error);
          this.$message.error('添加用户失败: ' + (error.response?.data?.message || error.message));
          this.loadingUsers = false;
        });
    },

    // 从角色中移除用户
    removeUserFromRole(user) {
      this.loadingUsers = true;
      UserService.removeRoleFromUser(user.id, this.assignUserForm.roleId)
        .then(response => {
          this.$message.success('用户移除成功');
          // 从已分配列表中移除用户
          const index = this.assignedUsers.findIndex(u => u.id === user.id);
          if (index !== -1) {
            this.assignedUsers.splice(index, 1);
          }
          this.loadingUsers = false;
        })
        .catch(error => {
          console.error('Error removing role from user:', error);
          this.$message.error('移除用户失败: ' + (error.response?.data?.message || error.message));
          this.loadingUsers = false;
        });
    },
    // 检查应用是否已分配
    isAppAssigned(appId) {
      return this.assignedApps.some(app => app.id === appId);
    },

    // 添加应用到角色
    addAppToRole(app) {
      this.loadingApps = true;
      UserService.assignRoleToApplication(app.id, this.assignAppForm.roleId)
        .then(response => {
          this.$message.success('应用添加成功');
          // 将应用添加到已分配列表
          this.assignedApps.push(app);
          this.loadingApps = false;
        })
        .catch(error => {
          console.error('Error assigning role to application:', error);
          this.$message.error('添加应用失败: ' + (error.response?.data?.message || error.message));
          this.loadingApps = false;
        });
    },

    // 从角色中移除应用
    removeAppFromRole(app) {
      this.loadingApps = true;
      UserService.removeRoleFromApplication(app.id, this.assignAppForm.roleId)
        .then(response => {
          this.$message.success('应用移除成功');
          // 从已分配列表中移除应用
          const index = this.assignedApps.findIndex(a => a.id === app.id);
          if (index !== -1) {
            this.assignedApps.splice(index, 1);
          }
          this.loadingApps = false;
        })
        .catch(error => {
          console.error('Error removing role from application:', error);
          this.$message.error('移除应用失败: ' + (error.response?.data?.message || error.message));
          this.loadingApps = false;
        });
    },
    formatRole(role) {
      switch(role) {
        case 'ROLE_USER': return '普通用户';
        case 'ROLE_ADMIN': return '管理员';
        case 'ROLE_SUPER_ADMIN': return '超级管理员';
        default: return role;
      }
    }
  }
};
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.role-table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.super-admin-note {
  color: #909399;
  font-style: italic;
}

.dialog-header {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.user-search, .app-search {
  margin-bottom: 15px;
}
</style>
