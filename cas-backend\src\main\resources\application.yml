spring:
  application:
    name: amphenol-cas
  datasource:
    # MySQL配置
    url: *****************************************************************************************************************************************
    username: fanruan
    password: fan<PERSON>an@123
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQL8Dialect
    hibernate:
      # 禁用JPA自动创建表结构
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        # 显示SQL参数值
        type: trace
  # 初始化数据库脚本（已禁用，因为脚本已在数据库中执行）
  sql:
    init:
      mode: never
      # schema-locations: classpath:schema-mysql.sql
      # data-locations: classpath:data-mysql.sql

server:
  port: 8080
  servlet:
    context-path: /

# JWT Configuration
jwt:
  secret: hUhE+NgPGP3SQe8FtWBIKMSk/+4ZVV4rwCVfAyJIBgsjUJlKU0ngFscV2MjMbheLrc9PEMCV25UlgQi9LDkEsA==
  expiration: 86400000 # 24 hours in milliseconds

# Logging
logging:
  level:
    root: INFO
    com.amphenol.cas: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
