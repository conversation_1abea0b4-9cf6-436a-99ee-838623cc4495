package com.amphenol.cas.controller;

import javax.naming.*;
import javax.naming.directory.*;
import java.util.Hashtable;

public class ADAuthValidator {

    public static void main(String[] args) {
        String username = "administrator"; // 用户名（不带域名）
        String password = "Q789+a456+z123.#"; // 用户密码
        String domain = "jet.com"; // AD域

        boolean isValid = validateADCredentials(username, password, domain);

        if (isValid) {
            System.out.println("√ 账号密码验证成功");
        } else {
            System.out.println("× 账号密码验证失败");
        }
    }

    public static boolean validateADCredentials(String username, String password, String domain) {
        // 1. 构造LDAP连接URL
        String ldapUrl = "ldap://" + getDomainController(domain) + ":389";

        // 2. 格式化用户Principal（支持多种格式）
        String[] principalFormats = {
                username + "@" + domain,                  // UPN格式：<EMAIL>
                "CN=" + username + ",CN=Users,DC=" + domain.replace(".", ",DC="), // DN格式
                domain + "\\" + username                  // 传统格式：
        };

        // 3. 尝试不同格式进行验证
        for (String principal : principalFormats) {
            System.out.println("尝试格式: " + principal);
            if (attemptValidation(ldapUrl, principal, password)) {
                return true;
            }
        }

        return false;
    }

    private static boolean attemptValidation(String ldapUrl, String principal, String password) {
        Hashtable<String, String> env = new Hashtable<>();
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, ldapUrl);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.SECURITY_PRINCIPAL, principal);
        env.put(Context.SECURITY_CREDENTIALS, password);

        // 设置超时和调试选项
        env.put("com.sun.jndi.ldap.connect.timeout", "5000");
        env.put("com.sun.jndi.ldap.read.timeout", "3000");
        env.put("com.sun.jndi.ldap.trace.ber", System.getProperty("DEBUG") != null ? "true" : "false");

        DirContext ctx = null;
        try {
            // 尝试建立连接
            ctx = new InitialDirContext(env);

            // 验证成功 - 执行简单查询确认
            Attributes attrs = ctx.getAttributes("");
            System.out.println("连接成功! 服务器信息: " + attrs.get("defaultNamingContext"));
            return true;
        } catch (AuthenticationException e) {
            System.out.println("认证失败: " + e.getMessage());
        } catch (NamingException e) {
            System.out.println("连接错误: " + e.getMessage());
        } finally {
            if (ctx != null) {
                try {
                    ctx.close();
                } catch (NamingException e) {
                }
            }
        }
        return false;
    }

    private static String getDomainController(String domain) {
        // 在实际应用中应该实现域控制器发现逻辑
        // 这里使用简化版：通过域名构造DC主机名

        // 示例：将"jet.com"转换为"dc1.jet.com"
        return "dc1." + domain;

        // 生产环境应使用以下方法之一：
        // 1. DNS SRV记录查询：_ldap._tcp.dc._msdcs.<domain>
        // 2. 配置文件指定多个DC
        // 3. 使用Round-Robin DNS
    }

    // 可选：DNS查询域控制器（需要网络权限）
    private static String discoverDomainController(String domain) {
        try {
            javax.naming.directory.DirContext dnsContext =
                    new javax.naming.directory.InitialDirContext();

            String dnsQuery = "SRV _ldap._tcp.dc._msdcs." + domain;
            javax.naming.directory.Attributes attributes =
                    dnsContext.getAttributes("dns:/" + dnsQuery, new String[]{"SRV"});

            javax.naming.directory.Attribute srvRecords = attributes.get("SRV");
            if (srvRecords != null) {
                for (int i = 0; i < srvRecords.size(); i++) {
                    String record = (String) srvRecords.get(i);
                    // 记录格式：优先级 权重 端口 主机名
                    String[] parts = record.split(" ");
                    if (parts.length >= 4) {
                        return parts[3].endsWith(".") ?
                                parts[3].substring(0, parts[3].length() - 1) :
                                parts[3];
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("DNS查询失败: " + e.getMessage());
        }
        return "dc1." + domain; // 退回默认值
    }
}
