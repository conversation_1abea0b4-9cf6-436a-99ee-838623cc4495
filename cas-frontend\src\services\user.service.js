import axios from 'axios';
import authHeader from './auth-header';

const API_URL = '/api/'; // 使用相对路径，axios.defaults.baseURL 已设置为 http://localhost:8080

class UserService {
  getUsers() {
    return axios.get(API_URL + 'users', { headers: authHeader() });
  }

  getUserById(id) {
    return axios.get(API_URL + `users/${id}`, { headers: authHeader() });
  }

  updateUser(id, user) {
    return axios.put(API_URL + `users/${id}`, user, { headers: authHeader() });
  }

  deleteUser(id) {
    return axios.delete(API_URL + `users/${id}`, { headers: authHeader() });
  }

  getApplications() {
    return axios.get(API_URL + 'applications', { headers: authHeader() });
  }

  getUserApplications() {
    return axios.get(API_URL + 'users/applications', { headers: authHeader() });
  }

  getApplicationById(id) {
    return axios.get(API_URL + `applications/${id}`, { headers: authHeader() });
  }

  createApplication(application) {
    return axios.post(API_URL + 'applications', application, { headers: authHeader() });
  }

  updateApplication(id, application) {
    return axios.put(API_URL + `applications/${id}`, application, { headers: authHeader() });
  }

  deleteApplication(id) {
    return axios.delete(API_URL + `applications/${id}`, { headers: authHeader() });
  }

  regenerateClientSecret(id) {
    return axios.post(API_URL + `applications/${id}/regenerate-secret`, {}, { headers: authHeader() });
  }

  getAuditLogs() {
    return axios.get(API_URL + 'audit-logs', { headers: authHeader() });
  }

  getAuditLogsByUsername(username) {
    return axios.get(API_URL + `audit-logs/user/${username}`, { headers: authHeader() });
  }

  getAuditLogsByAction(action) {
    return axios.get(API_URL + `audit-logs/action/${action}`, { headers: authHeader() });
  }

  getAuditLogsByDateRange(startDate, endDate) {
    return axios.get(API_URL + 'audit-logs/date-range', {
      headers: authHeader(),
      params: {
        startDate: startDate,
        endDate: endDate
      }
    });
  }

  // 角色管理
  getRoles() {
    return axios.get(API_URL + 'roles', { headers: authHeader() });
  }

  getRoleById(id) {
    return axios.get(API_URL + `roles/${id}`, { headers: authHeader() });
  }

  getUserRoles(userId) {
    return axios.get(API_URL + `roles/user/${userId}`, { headers: authHeader() });
  }

  getUsersByRole(roleId) {
    return axios.get(API_URL + `roles/${roleId}/users`, { headers: authHeader() });
  }

  createRole(roleData) {
    return axios.post(API_URL + 'roles', roleData, { headers: authHeader() });
  }

  updateRole(id, roleData) {
    return axios.put(API_URL + `roles/${id}`, roleData, { headers: authHeader() });
  }

  deleteRole(id, force = false) {
    return axios.delete(API_URL + `roles/${id}`, {
      headers: authHeader(),
      params: { force: force }
    });
  }

  assignRoleToUser(userId, roleId) {
    return axios.post(API_URL + 'roles/assign', null, {
      headers: authHeader(),
      params: {
        userId: userId,
        roleId: roleId
      }
    });
  }

  removeRoleFromUser(userId, roleId) {
    return axios.post(API_URL + 'roles/remove', null, {
      headers: authHeader(),
      params: {
        userId: userId,
        roleId: roleId
      }
    });
  }



  // 权限管理
  getApplicationRoles(applicationId) {
    return axios.get(API_URL + `permissions/application/${applicationId}`, { headers: authHeader() });
  }

  assignRoleToApplication(applicationId, roleId) {
    return axios.post(API_URL + 'permissions/assign', null, {
      headers: authHeader(),
      params: {
        applicationId: applicationId,
        roleId: roleId
      }
    });
  }

  removeRoleFromApplication(applicationId, roleId) {
    return axios.post(API_URL + 'permissions/remove', null, {
      headers: authHeader(),
      params: {
        applicationId: applicationId,
        roleId: roleId
      }
    });
  }
}

export default new UserService();
