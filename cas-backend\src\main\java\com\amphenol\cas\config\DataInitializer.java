package com.amphenol.cas.config;

import com.amphenol.cas.model.ERole;
import com.amphenol.cas.model.Role;
import com.amphenol.cas.model.User;
import com.amphenol.cas.repository.RoleRepository;
import com.amphenol.cas.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

/**
 * 数据初始化器，在应用启动时自动初始化必要的数据
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // 不再自动初始化角色，只初始化管理员用户
        initAdminUser();
    }

    /**
     * 初始化管理员用户
     */
    private void initAdminUser() {
        // 检查是否已经存在admin用户
        Optional<User> adminUserOpt = userRepository.findByUsername("admin");

        if (!adminUserOpt.isPresent()) {
            logger.info("初始化管理员用户");

            // 创建admin用户
            User adminUser = new User();
            adminUser.setUsername("admin");
            adminUser.setEmail("<EMAIL>");
            adminUser.setPassword(passwordEncoder.encode("admin123"));
            adminUser.setFullName("系统管理员");
            adminUser.setEnabled(true);
            adminUser.setAccountNonExpired(true);
            adminUser.setAccountNonLocked(true);
            adminUser.setCredentialsNonExpired(true);

            // 分配超级管理员角色
            Role superAdminRole = roleRepository.findByName(ERole.ROLE_SUPER_ADMIN)
                    .orElseThrow(() -> new RuntimeException("Error: Super Admin role not found."));

            Set<Role> roles = new HashSet<>();
            roles.add(superAdminRole);
            adminUser.setRoles(roles);

            userRepository.save(adminUser);
        } else {
            // 确保admin用户有超级管理员角色
            User adminUser = adminUserOpt.get();
            Role superAdminRole = roleRepository.findByName(ERole.ROLE_SUPER_ADMIN)
                    .orElseThrow(() -> new RuntimeException("Error: Super Admin role not found."));

            if (adminUser.getRoles() == null || !adminUser.getRoles().contains(superAdminRole)) {
                logger.info("为管理员用户分配超级管理员角色");
                Set<Role> roles = adminUser.getRoles() != null ? adminUser.getRoles() : new HashSet<>();
                roles.add(superAdminRole);
                adminUser.setRoles(roles);
                userRepository.save(adminUser);
            }
        }
    }
}
