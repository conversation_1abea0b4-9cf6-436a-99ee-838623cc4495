export default function authHeader() {
  let user = JSON.parse(localStorage.getItem('user'));
  console.log('Auth Header - User from localStorage:', user);

  if (user && user.token) {
    console.log('Auth Header - Token found:', user.token);
    // 确保令牌格式正确
    const token = user.token.startsWith('Bearer ') ? user.token : 'Bearer ' + user.token;
    console.log('Auth Header - Final token:', token);
    return { Authorization: token };
  } else {
    console.log('Auth Header - No token found');
    return {};
  }
}
