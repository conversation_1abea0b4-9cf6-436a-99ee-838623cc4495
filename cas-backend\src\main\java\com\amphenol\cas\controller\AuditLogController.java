package com.amphenol.cas.controller;

import com.amphenol.cas.model.AuditLog;
import com.amphenol.cas.repository.AuditLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/audit-logs")
public class AuditLogController {
    @Autowired
    AuditLogRepository auditLogRepository;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<AuditLog> getAllAuditLogs() {
        // 使用Sort.by创建一个按id降序排序的Sort对象
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        return auditLogRepository.findAll(sort);
    }

    @GetMapping("/user/{username}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<AuditLog> getAuditLogsByUsername(@PathVariable String username) {
        // 使用Sort.by创建一个按id降序排序的Sort对象
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        return auditLogRepository.findByUsername(username, sort);
    }

    @GetMapping("/action/{action}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<AuditLog> getAuditLogsByAction(@PathVariable String action) {
        // 使用Sort.by创建一个按id降序排序的Sort对象
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        return auditLogRepository.findByAction(action, sort);
    }

    @GetMapping("/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<AuditLog> getAuditLogsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        // 使用Sort.by创建一个按id降序排序的Sort对象
        Sort sort = Sort.by(Sort.Direction.DESC, "id");
        return auditLogRepository.findByTimestampBetween(startDate, endDate, sort);
    }
}
