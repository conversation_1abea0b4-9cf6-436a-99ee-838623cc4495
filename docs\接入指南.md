# 安费诺统一认证系统（CAS）接入指南

## 1. 概述

安费诺统一认证系统（CAS）是一个中央认证服务，提供单点登录（SSO）功能，允许用户使用一组凭据访问多个应用程序。本文档提供了如何将您的应用程序接入CAS系统的详细指南。

## 2. 接入流程

### 2.1 申请接入

1. 联系系统管理员申请接入CAS系统
2. 提供以下信息：
   - 应用名称
   - 应用描述
   - 回调URL（应用接收认证信息的地址）
   - 所需的用户角色权限

### 2.2 获取凭据

系统管理员会为您的应用创建一个应用记录，并提供以下凭据：
- Client ID：应用的唯一标识符
- Client Secret：用于安全通信的密钥

## 3. 认证流程

### 3.1 OAuth 2.0 授权码流程

CAS系统使用OAuth 2.0授权码流程进行认证，流程如下：

1. 用户访问您的应用
2. 应用将用户重定向到CAS登录页面
3. 用户在CAS系统中登录
4. CAS系统将用户重定向回您的应用，并提供授权码
5. 您的应用使用授权码向CAS系统请求访问令牌
6. CAS系统返回访问令牌和用户信息
7. 您的应用使用访问令牌获取用户信息并创建会话

### 3.2 重定向到CAS登录页面

```
GET https://cas.amphenol.com/api/oauth/authorize?
  response_type=code&
  client_id=YOUR_CLIENT_ID&
  redirect_uri=YOUR_REDIRECT_URI&
  scope=profile&
  state=RANDOM_STATE
```

参数说明：
- `response_type`：固定为"code"
- `client_id`：您的应用Client ID
- `redirect_uri`：您的应用回调URL
- `scope`：请求的权限范围，通常为"profile"
- `state`：随机字符串，用于防止CSRF攻击

### 3.3 接收授权码

用户成功登录后，CAS系统会将用户重定向到您的回调URL，并附加授权码：

```
GET YOUR_REDIRECT_URI?code=AUTHORIZATION_CODE&state=RANDOM_STATE
```

### 3.4 请求访问令牌

使用授权码请求访问令牌：

```
POST https://cas.amphenol.com/api/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code=AUTHORIZATION_CODE&
redirect_uri=YOUR_REDIRECT_URI&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET
```

### 3.5 接收访问令牌

CAS系统会返回访问令牌和用户信息：

```json
{
  "access_token": "ACCESS_TOKEN",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "REFRESH_TOKEN",
  "user_info": {
    "id": 123,
    "username": "user123",
    "email": "<EMAIL>",
    "fullName": "张三",
    "roles": ["ROLE_USER"]
  }
}
```

### 3.6 使用访问令牌

您可以使用访问令牌获取用户信息：

```
GET https://cas.amphenol.com/api/oauth/userinfo
Authorization: Bearer ACCESS_TOKEN
```

## 4. 单点登出

### 4.1 前端登出

当用户在您的应用中点击登出时，您应该：
1. 清除本地会话
2. 重定向用户到CAS登出页面

```
GET https://cas.amphenol.com/api/oauth/logout?
  client_id=YOUR_CLIENT_ID&
  redirect_uri=YOUR_LOGOUT_REDIRECT_URI
```

### 4.2 后端通知

CAS系统还支持后端通知登出，当用户在其他应用登出时，CAS系统会向您的应用发送登出通知：

```
POST YOUR_LOGOUT_CALLBACK_URL
Content-Type: application/json

{
  "event": "logout",
  "user_id": 123,
  "timestamp": 1623456789
}
```

## 5. 安全建议

1. 保护Client Secret，不要在前端代码中暴露
2. 验证state参数，防止CSRF攻击
3. 使用HTTPS进行所有通信
4. 定期更新Client Secret
5. 验证令牌的有效性和过期时间

## 6. 示例代码

### 6.1 Java示例

```java
// 重定向到CAS登录页面
@GetMapping("/login")
public String login(HttpServletRequest request) {
    String state = generateRandomState();
    request.getSession().setAttribute("oauth_state", state);
    
    return "redirect:https://cas.amphenol.com/api/oauth/authorize?" +
           "response_type=code&" +
           "client_id=YOUR_CLIENT_ID&" +
           "redirect_uri=YOUR_REDIRECT_URI&" +
           "scope=profile&" +
           "state=" + state;
}

// 处理回调
@GetMapping("/callback")
public String callback(@RequestParam String code, @RequestParam String state, HttpServletRequest request) {
    // 验证state
    String savedState = (String) request.getSession().getAttribute("oauth_state");
    if (!state.equals(savedState)) {
        throw new RuntimeException("Invalid state parameter");
    }
    
    // 请求访问令牌
    // 使用RestTemplate或HttpClient发送POST请求
    // ...
    
    // 保存用户会话
    // ...
    
    return "redirect:/dashboard";
}
```

### 6.2 JavaScript示例

```javascript
// 重定向到CAS登录页面
function login() {
    const state = generateRandomState();
    localStorage.setItem('oauth_state', state);
    
    window.location.href = 'https://cas.amphenol.com/api/oauth/authorize?' +
                          'response_type=code&' +
                          'client_id=YOUR_CLIENT_ID&' +
                          'redirect_uri=YOUR_REDIRECT_URI&' +
                          'scope=profile&' +
                          'state=' + state;
}

// 处理回调
async function handleCallback() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    // 验证state
    const savedState = localStorage.getItem('oauth_state');
    if (state !== savedState) {
        throw new Error('Invalid state parameter');
    }
    
    // 请求访问令牌
    // 注意：这通常应该在后端完成，以保护client_secret
    const tokenResponse = await fetch('https://cas.amphenol.com/api/oauth/token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': 'YOUR_REDIRECT_URI',
            'client_id': 'YOUR_CLIENT_ID',
            'client_secret': 'YOUR_CLIENT_SECRET'
        })
    });
    
    const tokenData = await tokenResponse.json();
    
    // 保存访问令牌和用户信息
    localStorage.setItem('access_token', tokenData.access_token);
    localStorage.setItem('user_info', JSON.stringify(tokenData.user_info));
    
    // 重定向到应用首页
    window.location.href = '/dashboard';
}
```

## 7. 常见问题

### 7.1 令牌过期

访问令牌有效期通常为1小时。当令牌过期时，您可以使用刷新令牌获取新的访问令牌：

```
POST https://cas.amphenol.com/api/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=refresh_token&
refresh_token=REFRESH_TOKEN&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET
```

### 7.2 错误处理

CAS系统返回的常见错误码：

- `invalid_request`：请求缺少必要参数
- `invalid_client`：Client ID或Client Secret无效
- `invalid_grant`：授权码或刷新令牌无效或已过期
- `unauthorized_client`：客户端未被授权使用此授权类型
- `access_denied`：用户拒绝授权
- `unsupported_grant_type`：不支持的授权类型

## 8. 联系支持

如有任何问题或需要帮助，请联系：

- 邮箱：<EMAIL>
- 电话：123-456-7890
