# 安费诺统一认证系统（CAS）后端

## 项目概述

安费诺统一认证系统（CAS）后端是基于Spring Boot开发的认证服务，提供用户认证、授权和单点登录功能。

## 技术栈

- Java 11
- Spring Boot 2.7.x
- Spring Security
- Spring Data JPA
- JWT
- MySQL

## 开发环境配置

### 前提条件

- JDK 11+
- Maven 3.6+
- MySQL 8.0+

### 构建项目

```bash
mvn clean install
```

### 运行项目

```bash
mvn spring-boot:run
```

应用将在 http://localhost:8080/api 运行。

## 数据库配置

### MySQL配置

系统使用MySQL数据库。请按照以下步骤配置：

1. 创建MySQL数据库

```sql
CREATE DATABASE amphenol_cas CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改`application.yml`配置文件

取消注释MySQL相关配置，并注释H2相关配置：

```yaml
spring:
  datasource:
    # 注释H2配置
    # url: jdbc:h2:mem:casdb
    # username: sa
    # password: password
    # driver-class-name: org.h2.Driver

    # 取消注释MySQL配置
    url: *****************************************************************************************************
    username: root  # 修改为您的MySQL用户名
    password: your_password  # 修改为您的MySQL密码
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    # 注释H2方言
    # database-platform: org.hibernate.dialect.H2Dialect

    # 取消注释MySQL方言
    database-platform: org.hibernate.dialect.MySQL8Dialect

    hibernate:
      # 禁用JPA自动创建表结构
      ddl-auto: none

  # 注释H2控制台配置
  # h2:
  #   console:
  #     enabled: true
  #     path: /h2-console

  # 使用SQL脚本初始化数据库
  sql:
    init:
      mode: always
      # 注释H2脚本
      # schema-locations: classpath:schema-h2.sql
      # data-locations: classpath:data-h2.sql
      # 取消注释MySQL脚本
      schema-locations: classpath:schema-mysql.sql
      data-locations: classpath:data-mysql.sql
```

3. 初始化数据库

系统将使用SQL脚本自动初始化数据库：
- `schema-mysql.sql`：创建表结构
- `data-mysql.sql`：插入初始数据

您也可以手动执行这些脚本来初始化数据库。

## 默认账户

系统初始化时会创建一个默认的管理员账户：
- 用户名：`admin`
- 密码：`admin123`

## API文档

### 认证API

- `POST /api/auth/signin`：用户登录
- `POST /api/auth/signup`：用户注册

### 用户API

- `GET /api/users`：获取所有用户（需要管理员权限）
- `GET /api/users/{id}`：获取指定用户
- `PUT /api/users/{id}`：更新用户信息
- `DELETE /api/users/{id}`：删除用户（需要超级管理员权限）

### 应用API

- `GET /api/applications`：获取所有应用（需要管理员权限）
- `GET /api/applications/{id}`：获取指定应用
- `POST /api/applications`：创建应用（需要管理员权限）
- `PUT /api/applications/{id}`：更新应用信息（需要管理员权限）
- `DELETE /api/applications/{id}`：删除应用（需要超级管理员权限）
- `POST /api/applications/{id}/regenerate-secret`：重新生成客户端密钥（需要管理员权限）

### 审计日志API

- `GET /api/audit-logs`：获取所有审计日志（需要管理员权限）
- `GET /api/audit-logs/user/{username}`：获取指定用户的审计日志（需要管理员权限）
- `GET /api/audit-logs/action/{action}`：获取指定操作的审计日志（需要管理员权限）
- `GET /api/audit-logs/date-range`：获取指定日期范围的审计日志（需要管理员权限）
