<template>
  <div class="dashboard">
    <!-- 用户可访问的应用列表 -->
    <h2 class="section-title">我的应用</h2>
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else-if="userApplications.length === 0" class="empty-apps">
      <el-empty description="暂无可访问的应用" />
    </div>
    <el-row :gutter="20" v-else>
      <el-col :span="8" v-for="app in userApplications" :key="app.id">
        <el-card class="app-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>{{ app.name }}</span>
              <el-button
                class="button"
                type="primary"
                size="small"
                @click="openApplication(app)"
              >访问</el-button>
            </div>
          </template>
          <div class="app-content">
            <p class="app-description">{{ app.description || '暂无描述' }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserService from '@/services/user.service';

export default {
  name: 'Dashboard',
  data() {
    return {
      loading: true,
      userApplications: []
    };
  },
  computed: {
    currentUser() {
      return this.$store.state.auth.user;
    },
    userRoles() {
      if (this.currentUser && this.currentUser.roles) {
        return this.currentUser.roles.map(role => {
          switch(role) {
            case 'ROLE_USER': return '普通用户';
            case 'ROLE_ADMIN': return '管理员';
            case 'ROLE_SUPER_ADMIN': return '超级管理员';
            default: return role;
          }
        }).join(', ');
      }
      return '';
    },
    showAdminBoard() {
      if (this.currentUser && this.currentUser.roles) {
        return this.currentUser.roles.includes('ROLE_ADMIN') ||
               this.currentUser.roles.includes('ROLE_SUPER_ADMIN');
      }
      return false;
    },
    isSuperAdmin() {
      return this.currentUser && this.currentUser.roles &&
             this.currentUser.roles.includes('ROLE_SUPER_ADMIN');
    }
  },
  created() {
    this.loadUserApplications();
  },
  methods: {
    loadUserApplications() {
      this.loading = true;

      // 如果是超级管理员，获取所有应用
      if (this.isSuperAdmin) {
        UserService.getApplications()
          .then(response => {
            this.userApplications = response.data;
            this.loading = false;
          })
          .catch(error => {
            console.error('Error loading applications:', error);
            this.$message.error('加载应用列表失败');
            this.loading = false;
          });
      } else {
        // 否则获取用户有权限的应用
        UserService.getUserApplications()
          .then(response => {
            this.userApplications = response.data;
            this.loading = false;
          })
          .catch(error => {
            console.error('Error loading user applications:', error);
            this.$message.error('加载应用列表失败');
            this.loading = false;
          });
      }
    },
    openApplication(app) {
      // 如果是统一认证平台，直接导航到相应页面
      if (app.clientId === 'cas-platform') {
        // 根据用户角色决定导航到哪个页面
        if (this.showAdminBoard) {
          this.$router.push('/admin/users');
        } else {
          this.$router.push('/profile');
        }
      } else {
        // 对于外部应用，打开其重定向URL
        if (app.redirectUrl) {
          window.open(app.redirectUrl, '_blank');
        } else {
          this.$message.warning('应用未配置访问地址');
        }
      }
    }
  }
};
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  min-height: 100px;
}

.user-info p {
  margin: 10px 0;
}

.mt-20 {
  margin-top: 20px;
}

.section-title {
  margin-top: 30px;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.app-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.app-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app-content {
  min-height: 80px;
}

.app-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.loading-container {
  padding: 20px;
}

.empty-apps {
  margin: 40px 0;
  text-align: center;
}
</style>
